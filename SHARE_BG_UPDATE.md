# 分享背景图更新

## 更新概述

根据新的设计要求，已将分享页面更新为使用新的分享背景图 `assets/share-bg.png`，该图片包含了所有的文字和设计元素，只需要在白色框中填入答对题数和挑战用时即可。

## 主要变更

### 1. 背景图片更换 ✅
- **原背景**: 渐变背景 + 复杂的文字和元素绘制
- **新背景**: 使用 `assets/share-bg.png` 作为完整的背景图
- **优势**: 设计更统一，减少Canvas绘制复杂度

### 2. 简化Canvas绘制逻辑 ✅
- **移除内容**:
  - 渐变背景绘制
  - 标题文字绘制 ("Gusto" + "英语发音挑战结果")
  - 二维码绘制
  - 白色成绩卡片绘制
  - 圆角矩形辅助方法

- **保留内容**:
  - 背景图片绘制
  - 答对题数和挑战用时的数字填入

### 3. 页面布局优化 ✅
- **移除顶部区域**: 去掉了"分享挑战结果"标题和返回按钮
- **黑色背景**: 页面背景改为纯黑色 (#000000)
- **全屏显示**: Canvas居中显示，底部固定分享按钮
- **安全区域覆盖**: 通过页面配置和CSS确保黑色背景覆盖顶部安全区域

### 4. 数据填入优化 ✅
- **位置**: 在白色框中央位置 (y=320)
- **字体**: 36px，橙红色 (#FF4500)
- **布局**:
  - 左侧: 答对题数 (x = canvasWidth/2 - 60)
  - 右侧: 挑战用时 (x = canvasWidth/2 + 60)

### 5. 代码简化 ✅
- 移除了 `drawRoundedRect` 方法
- 移除了 `goBack` 方法和相关绑定
- 移除了header相关的JSX和样式
- 简化了Canvas绘制流程
- 减少了代码复杂度和维护成本

## 技术实现

### Canvas绘制流程
```typescript
// 1. 绘制完整的分享背景图
ctx.drawImage('/assets/share-bg.png', 0, 0, canvasWidth, canvasHeight)

// 2. 在白色框中填入答对题数
ctx.setFillStyle('#FF4500')
ctx.setFontSize(36)
ctx.setTextAlign('center')
ctx.fillText(`${shareData.score}`, canvasWidth / 2 - 60, 320)

// 3. 在白色框中填入挑战用时
ctx.fillText(`${ShareService.formatDuration(shareData.duration)}`, canvasWidth / 2 + 60, 320)
```

### 降级方案
如果分享背景图加载失败，会自动降级到原来的渐变背景：
```typescript
// 降级方案：使用渐变背景
const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
gradient.addColorStop(0, '#FFB6C1')
gradient.addColorStop(0.3, '#DDA0DD')
gradient.addColorStop(0.7, '#87CEEB')
gradient.addColorStop(1, '#98FB98')
ctx.setFillStyle(gradient)
ctx.fillRect(0, 0, canvasWidth, canvasHeight)
```

## 文件变更

### 修改的文件
- `src/packages/user/pages/share/index.tsx` - 简化Canvas绘制逻辑，移除header区域
- `src/packages/user/pages/share/index.scss` - 更新为黑色背景，移除header样式，修复安全区域覆盖

### 新增的文件
- `src/packages/user/pages/share/index.config.ts` - 页面配置，设置黑色导航栏和背景

### 新增的资源
- `src/assets/share-bg.png` - 新的分享背景图

### 移除的代码
- `drawRoundedRect` 方法
- `goBack` 方法和相关绑定
- header区域的JSX结构
- header相关的CSS样式
- 标题文字绘制逻辑
- 二维码绘制逻辑
- 成绩卡片绘制逻辑

## 优势

### 1. 设计一致性 ✅
- 使用设计师提供的完整背景图
- 避免了代码绘制与设计稿的差异
- 确保视觉效果完全符合设计要求
- 黑色背景提供更好的视觉对比

### 2. 性能优化 ✅
- 减少了Canvas绘制操作
- 降低了CPU使用率
- 提升了分享图生成速度
- 移除了不必要的UI元素

### 3. 维护简化 ✅
- 代码逻辑更简单
- 减少了出错的可能性
- 设计变更只需要替换背景图
- 移除了复杂的header逻辑

### 4. 用户体验提升 ✅
- 全屏显示分享图，更加沉浸
- 黑色背景突出分享内容
- 简化的界面减少干扰
- 专注于分享功能本身

### 5. 灵活性提升 ✅
- 设计师可以直接提供完整的背景图
- 无需前端重新实现复杂的设计元素
- 支持更复杂的视觉效果

## 使用说明

### 位置调整
如果需要调整答对题数和挑战用时的位置，只需要修改 `dataY` 变量：
```typescript
const dataY = 320  // 根据实际分享图中白色框的位置调整
```

### 字体调整
如果需要调整字体大小或颜色：
```typescript
ctx.setFillStyle('#FF4500')  // 颜色
ctx.setFontSize(36)          // 字体大小
```

### 布局调整
如果需要调整左右位置：
```typescript
ctx.fillText(`${shareData.score}`, canvasWidth / 2 - 60, dataY)        // 左侧位置
ctx.fillText(`${duration}`, canvasWidth / 2 + 60, dataY)               // 右侧位置
```

## 测试建议

1. 在微信开发者工具中测试新的分享背景图显示效果
2. 验证答对题数和挑战用时的位置是否准确
3. 测试不同数据长度的显示效果
4. 验证降级方案是否正常工作
5. 测试分享功能的各个选项是否正常

## 后续优化

1. 根据实际效果微调数字位置
2. 如需要可以调整字体样式
3. 可以考虑添加数字的阴影或描边效果
4. 支持更多的数据展示格式
