# URL参数处理修复

## 问题描述
当用户失败3次后提示正确答案时，显示的不是单词名称，而是包含URL参数的完整文件名。

例如：
- 原始URL: `https://gusto-english-oss.wemore.com/mp/guess_word/car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300`
- 期望结果: `car`
- 实际显示: `car` (如果没有参数) 或 `car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300` (如果有参数)

## 问题原因
原来的 `getCorrectAnswerFromImage` 函数只是简单地用 `.` 分割文件名，没有考虑URL参数的情况：

```typescript
// 修复前的代码
const fileName = imageUrl.split('/').pop() || ''
const wordName = fileName.split('.')[0] || ''  // 这里没有处理URL参数
```

当URL包含参数时，`fileName` 是 `"car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300"`，直接用 `.` 分割会得到 `"car"`，但如果逻辑有问题可能返回整个字符串。

## 修复方案

### 更新后的函数逻辑：

```typescript
getCorrectAnswerFromImage = (imageUrl: string): string => {
  try {
    console.log('原始图片URL:', imageUrl)
    
    // 1. 从URL中提取文件名
    const fileName = imageUrl.split('/').pop() || ''
    console.log('提取的文件名:', fileName)
    
    // 2. 先去除URL参数（如 ?resize=xxx）
    const fileNameWithoutParams = fileName.split('?')[0]
    console.log('去除参数后的文件名:', fileNameWithoutParams)
    
    // 3. 去除文件扩展名
    const wordName = fileNameWithoutParams.split('.')[0] || ''
    console.log('最终提取的单词:', wordName)
    
    return wordName
  } catch (error) {
    console.error('提取图片文件名失败:', error)
    return 'unknown'
  }
}
```

### 处理步骤详解：

1. **提取文件名部分**：
   - 输入: `https://gusto-english-oss.wemore.com/mp/guess_word/car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300`
   - 结果: `car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300`

2. **去除URL参数**：
   - 输入: `car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300`
   - 结果: `car.png`

3. **去除文件扩展名**：
   - 输入: `car.png`
   - 结果: `car`

### 添加调试日志

为了便于排查问题，每个步骤都添加了 console.log，可以在控制台看到：
```
原始图片URL: https://gusto-english-oss.wemore.com/mp/guess_word/car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300
提取的文件名: car.png?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300
去除参数后的文件名: car.png
最终提取的单词: car
```

## 测试用例

| 输入URL | 期望输出 |
|---------|----------|
| `https://example.com/car.png` | `car` |
| `https://example.com/car.png?resize=300` | `car` |
| `https://example.com/apple.jpg?x-oss-process=image/resize,m_lfit,limit_1,w_300,h_300` | `apple` |
| `https://example.com/banana.webp?quality=80&format=auto` | `banana` |

现在当用户失败3次后，会正确显示 "正确答案是: car" 而不是完整的URL字符串。
