# Claude AI 项目说明文档

## 📋 项目概述

**项目名称**: 英语发音练习小程序 (word-pronunciation-game)  
**项目类型**: 微信小程序  
**版本**: 1.5.0  
**主要功能**: 通过语音识别技术帮助用户练习英语发音，提供图片猜词游戏、发音评估、排行榜竞技、历史记录查看等完整的英语学习体验

## 🛠️ 技术栈

### 核心框架
- **Taro**: 4.1.4 (多端统一开发框架)
- **React**: 18.0.0 (UI 框架)
- **TypeScript**: 5.4.5 (类型安全的 JavaScript 超集)

### 样式与构建
- **Sass**: 1.75.0 (CSS 预处理器)
- **Webpack**: 5.91.0 (模块打包工具)
- **PostCSS**: 8.4.38 (CSS 后处理器)

### 开发工具
- **ESLint**: 8.57.0 (代码质量检查)
- **Stylelint**: 16.4.0 (样式代码检查)
- **Husky**: 9.1.7 (<PERSON><PERSON> hooks 管理)
- **Commitlint**: 19.8.1 (提交信息规范检查)

### 监控与错误追踪
- **Sentry**: 9.43.0 (错误监控和性能追踪)

## 📦 包管理器

**使用 Yarn 进行包管理**

```bash
# 安装依赖
yarn install

# 开发模式 (微信小程序)
yarn dev:weapp

# 构建微信小程序
yarn build:weapp

# 开发模式 (H5)
yarn dev:h5

# 构建 H5
yarn build:h5
```

## 🏗️ 项目结构

```
word-pronunciation-game/
├── src/                    # 源代码目录
│   ├── app.config.ts      # 全局配置
│   ├── app.scss           # 全局样式
│   ├── app.ts             # 应用入口
│   ├── index.html         # H5 入口文件
│   ├── assets/            # 静态资源
│   │   └── bg.png         # 背景图片
│   ├── data/              # 数据文件
│   │   ├── words.json     # 单词数据
│   │   └── words.ts       # 单词类型定义
│   ├── pages/             # 页面组件
│   │   ├── index/         # 首页 - 主菜单界面
│   │   ├── game/          # 游戏页面 - 发音挑战和猜词游戏
│   │   ├── result/        # 结果页面 - 游戏结果展示
│   │   ├── history/       # 历史记录页面
│   │   ├── share/         # 分享页面
│   │   ├── endless-mode/  # 无尽模式选择页面
│   │   └── ranking/       # 排行榜页面 - 多种排行榜类型
│   ├── services/          # 服务层
│   │   ├── api.ts         # API 服务 - 后端接口调用
│   │   ├── voice.ts       # 语音服务 - 语音识别
│   │   ├── wordService.ts # 单词服务 - 单词数据管理
│   │   ├── userService.ts # 用户服务 - 微信登录和用户管理
│   │   ├── historyService.ts # 历史记录服务
│   │   ├── shareService.ts   # 分享服务
│   │   ├── audioService.ts   # 音频播放服务
│   │   ├── customerService.ts # 客服服务
│   │   └── rankingService.ts  # 排行榜服务
│   ├── sound/             # 音频文件
│   │   ├── Correct_Sound.mp3    # 正确答案音效
│   │   └── Wrong_Sound.mp3      # 错误答案音效
│   ├── types/             # 类型定义
│   │   ├── game.ts        # 游戏相关类型
│   │   └── user.ts        # 用户相关类型
│   └── utils/             # 工具函数
│       └── sentry.ts      # Sentry 配置
├── config/                # 配置文件
│   ├── dev.ts            # 开发环境配置
│   ├── prod.ts           # 生产环境配置
│   └── index.ts          # 配置入口
├── docs/                 # 文档目录
├── types/                # 全局类型定义
└── 配置文件们...
```

## 🎯 核心功能

### 1. 语音识别练习
- 使用微信小程序原生语音识别 API
- 实时语音转文字功能
- 发音准确性评估
- 支持发音挑战模式和看图猜词模式

### 2. 多种游戏模式
- **发音挑战**：听单词发音，用户跟读练习
- **看图猜词**：图片展示，用户猜测单词并语音输入
- **无尽模式**：不限次数的挑战练习，支持猜词和发音两种模式

### 3. 用户系统
- **微信登录**：一键微信授权登录
- **用户信息管理**：头像、昵名、手机号获取
- **登录状态持久化**：本地存储用户信息

### 4. 排行榜系统
- **猜单词排行榜**：按最高分排序
- **发音挑战排行榜**：按发音准确度排序  
- **本周挑战次数排行榜**：统计最近7天挑战次数
- **总挑战次数排行榜**：统计历史总挑战次数
- **实时数据同步**：与后端API对接获取真实排行数据

### 5. 历史记录
- **游戏历史查看**：详细的游戏记录
- **成绩统计分析**：正确率、用时、得分等数据
- **学习进度追踪**：长期学习效果监测

### 6. 社交分享
- **成绩分享**：生成分享图片
- **微信分享**：朋友圈和好友分享
- **客服系统**：福利群二维码和在线客服

### 7. 音效与反馈
- **音效系统**：正确/错误音效反馈
- **视觉反馈**：动画和UI状态反馈
- **语音播放**：单词发音播放功能

### 8. 多平台支持
- 微信小程序 (主要平台)
- H5 网页版
- 支持其他小程序平台扩展

## 🔧 开发环境设置

### 前置要求
- Node.js 16+
- Yarn 包管理器
- 微信开发者工具

### 快速开始

1. **克隆项目并安装依赖**
```bash
git clone <repository-url>
cd word-pronunciation-game
yarn install
```

2. **启动开发服务器 (微信小程序)**
```bash
yarn dev:weapp
```

3. **使用微信开发者工具**
- 打开微信开发者工具
- 导入项目目录中的 `dist` 文件夹
- 开始开发调试

### 构建部署

```bash
# 构建微信小程序
yarn build:weapp

# 构建 H5 版本
yarn build:h5
```

## 📱 小程序信息

- **小程序 ID**: wx3e6eb773e7f9b12e
- **主要页面**:
  - 首页 (`/pages/index/index`) - 主菜单，四个功能卡片
  - 游戏页面 (`/pages/game/index`) - 发音挑战和猜词游戏
  - 结果页面 (`/pages/result/index`) - 游戏结果展示和分享
  - 历史记录页面 (`/pages/history/index`) - 游戏历史查看
  - 无尽模式页面 (`/pages/endless-mode/index`) - 无尽模式选择
  - 排行榜页面 (`/pages/ranking/index`) - 多种排行榜展示
  - 分享页面 (`/pages/share/index`) - 分享图片生成

## 🔍 代码质量

项目配置了完整的代码质量检查工具：

- **ESLint**: JavaScript/TypeScript 代码规范检查
- **Stylelint**: 样式代码规范检查
- **Commitlint**: Git 提交信息规范检查
- **Husky**: 自动化 Git hooks
- **Lint-staged**: 暂存文件检查

## 🎨 样式规范

- 使用 Sass 预处理器
- 遵循 BEM 命名规范
- 响应式设计，适配不同屏幕尺寸
- 组件样式模块化

## 📊 监控与分析

集成 Sentry 进行：
- 错误监控和追踪
- 性能监控
- 用户行为分析

## 🌐 后端API集成

### API 基础配置
- **测试环境**: `https://apitest.wemore.com/word-guess`
- **正式环境**: `https://api.wemore.com/word-guess`
- **自动环境切换**: 根据小程序版本（开发/体验/正式）自动切换API地址

### 主要API接口

#### 单词相关
- `GET /api/v1/words/random` - 获取随机单词
- `GET /api/v1/words/pronunce_challenge` - 获取发音挑战单词
- `GET /words` - 获取所有单词
- `GET /words/:id` - 根据ID获取单词
- `GET /words/search` - 搜索单词

#### 游戏结果相关  
- `POST /api/v1/game-results` - 提交游戏结果
- `GET /api/v1/game-results/{openId}` - 获取用户游戏历史

#### 用户数据结构
```typescript
// 游戏结果提交
{
  "openId": "string",
  "score": 8,
  "totalWords": 10,
  "accuracy": 80.0,
  "duration": 45000,
  "wrongWords": [{"word": "apple", "attempts": 2}],
  "completedAt": 1692345678000,
  "gameMode": "pronunciation"
}
```

### 数据流程
1. **用户登录** → 获取微信OpenId → 本地存储用户信息
2. **游戏结束** → 提交成绩到后端 → 更新排行榜数据
3. **查看排行榜** → 从后端获取用户历史 → 计算排名显示
4. **离线模式** → API失败时使用本地数据和模拟数据

## 🚀 部署流程

1. 代码开发完成后提交到代码仓库
2. 运行 `yarn build:weapp` 构建小程序版本
3. 使用微信开发者工具上传代码
4. 在微信公众平台提交审核
5. 审核通过后发布上线

## 📝 开发注意事项

### Taro 开发要点
- 使用 Taro 提供的组件和 API
- 注意不同平台的兼容性差异
- 遵循 Taro 的目录结构和命名规范
- 页面配置文件 (`index.config.ts`) 用于自定义导航栏

### 小程序开发限制
- 包大小限制 (主包 2MB，分包 20MB)
- API 调用限制和权限申请
- 域名白名单配置要求
- 录音权限需要用户授权

### 性能优化
- 图片资源使用 CDN
- 合理使用分包加载
- 避免过度嵌套组件
- 音频文件预加载和缓存

### 用户体验要点
- **安全区域适配**: 使用 `env(safe-area-inset-*)` 适配刘海屏
- **自定义导航栏**: 排行榜等页面使用 `navigationStyle: 'custom'`
- **错误处理**: API失败时优雅降级到本地数据
- **加载状态**: 提供良好的loading和反馈体验
- **音效反馈**: 正确/错误操作有对应音效

### 代码规范
- TypeScript 严格模式，确保类型安全
- 服务层模块化，单一职责原则
- 错误边界处理，避免白屏
- 日志记录使用 console.log 和 Sentry

## 🤝 协作开发

1. 遵循 Git Flow 工作流
2. 提交信息格式：`type(scope): description`
3. 代码审查必须通过才能合并
4. 保持代码注释和文档更新

## 🆕 最新功能特性 (v1.5.0)

### 🏆 排行榜系统
- **多维度排行榜**: 猜单词、发音挑战、周挑战次数、总挑战次数
- **实时数据同步**: 与后端API对接，获取真实用户数据
- **美观UI设计**: 清新蓝色渐变背景，奖牌emoji显示前三名
- **登录状态管理**: 未登录显示蒙板引导，已登录显示个人按钮

### 👤 用户系统升级  
- **微信一键登录**: 获取用户头像、昵称信息
- **稳定OpenId生成**: 基于设备信息生成一致性用户标识
- **登录状态持久化**: 本地存储，应用重启后保持登录状态
- **用户信息管理**: 支持手机号授权获取

### 🌐 后端API集成
- **环境自动切换**: 开发/测试/正式环境自动识别
- **游戏结果提交**: 每局游戏结束自动提交成绩到后端
- **历史数据获取**: 从服务端获取用户完整游戏历史
- **容错机制**: API失败时优雅降级到本地数据

### 🎨 UI/UX 优化
- **安全区域适配**: 完美支持刘海屏、挖孔屏等异形屏
- **自定义导航栏**: 沉浸式设计，背景渐变覆盖状态栏
- **音效系统**: 正确/错误操作音效反馈
- **加载状态**: 完善的loading和错误提示

### 🏗️ 架构改进
- **服务层重构**: 模块化设计，单一职责原则
- **类型安全**: 完善的TypeScript类型定义
- **错误监控**: Sentry集成，线上问题实时追踪
- **代码质量**: ESLint、Stylelint等工具保证代码规范

---

*最后更新: 2025-08-18*  
*当前版本: v1.5.0*
