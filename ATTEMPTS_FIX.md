# 游戏尝试次数逻辑修复

## 问题描述
发音识别失败后，尝试次数始终显示 1/3，无法正确递增到 2/3，导致用户可以无限重试而不会显示正确答案。

## 问题原因
在原来的代码中，setState 的调用存在状态覆盖问题：
1. 第一次 setState 更新了 attempts
2. 第二次 setState 使用了过时的 this.state，导致 attempts 被重置

## 修复方案

### 1. 合并状态更新
将尝试次数的更新和其他状态更新合并到同一个 setState 调用中：

```typescript
// 修复前：分两次 setState，容易导致状态覆盖
const newAttempts = this.state.gameState.attempts + 1
this.setState(prevState => ({
  gameState: { ...prevState.gameState, attempts: newAttempts }
}))
// ... 其他逻辑
this.setState({
  feedback: `发音不太对，再试试看！(${newAttempts}/3)`,
  gameState: { ...this.state.gameState, isRecording: false } // 这里会覆盖之前的 attempts
})

// 修复后：一次性更新所有相关状态
this.setState(prevState => ({
  feedback: `发音不太对，再试试看！(${newAttempts}/3)`,
  gameState: { 
    ...prevState.gameState, 
    attempts: newAttempts,
    isRecording: false 
  }
}))
```

### 2. 添加调试日志
增加 console.log 来跟踪尝试次数的变化：

```typescript
console.log(`发音识别失败，当前尝试次数: ${this.state.gameState.attempts} -> ${newAttempts}`)
```

### 3. 界面显示尝试次数
在游戏界面顶部添加尝试次数显示：

```tsx
<Text className='attempts'>尝试: {gameState.attempts}/3</Text>
```

## 修复后的逻辑流程

1. **第1次失败**：attempts = 0 → 1，显示 "发音不太对，再试试看！(1/3)"
2. **第2次失败**：attempts = 1 → 2，显示 "发音不太对，再试试看！(2/3)"  
3. **第3次失败**：attempts = 2 → 3，显示 "正确答案是: xxx"，2秒后自动进入下一个单词
4. **下一个单词**：attempts 重置为 0

## 测试验证

现在用户在发音识别失败时：
- 界面顶部会显示当前尝试次数 "尝试: X/3"
- 反馈信息会正确显示 "(X/3)"
- 失败3次后会自动显示正确答案并进入下一个单词
- 控制台会输出调试信息帮助追踪状态变化

这个修复确保了游戏的挑战性和公平性，用户不能无限重试同一个单词。
