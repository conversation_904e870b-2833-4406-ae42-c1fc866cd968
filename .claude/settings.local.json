{"permissions": {"allow": ["Bash(npm run lint)", "Bash(find:*)", "Bash(yarn build:*)", "mcp__linear-server__get_issue", "mcp__linear-server__list_issues", "Bash(yarn lint)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(yarn dev:weapp:*)", "WebFetch(domain:developers.weixin.qq.com)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "mcp__linear-server__create_issue", "Bash(yarn tsc:*)", "Bash(node:*)"], "deny": []}}