.history-container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #1e3c72 0%, #2a5298 50%, #6a11cb 100%);
  padding: 20px;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 5px;

  .back-btn,
  .clear-btn {
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 14px;
    font-weight: bold;
    backdrop-filter: blur(4px);
  }

  .back-btn:active,
  .clear-btn:active {
    opacity: 0.8;
    transform: scale(0.95);
  }

  .page-title {
    font-size: 24px;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
}

.loading-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 100px;

  .loading-text {
    font-size: 24px;
    color: white;
    font-weight: bold;
    text-align: center;
    animation: loading-pulse 1.5s infinite;
  }
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.empty-state {
  text-align: center;
  margin-top: 100px;
  padding: 40px 20px;

  .empty-icon {
    font-size: 80px;
    display: block;
    margin-bottom: 20px;
  }

  .empty-text {
    font-size: 24px;
    color: white;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .empty-tip {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.history-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    .item-left {
      display: flex;
      align-items: center;
      gap: 10px;

      .item-number {
        font-size: 18px;
        font-weight: bold;
        color: #1e3c72;
      }

      .game-mode-badge {
        font-size: 14px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 600;
        
        &[data-mode="pronunciation"] {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
      }
    }

    .item-date {
      font-size: 16px;
      color: #666;
    }
  }

  .score-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;

    .score-main {
      display: flex;
      align-items: center;
      gap: 8px;

      .score-label {
        font-size: 16px;
        color: #666;
        font-weight: 500;
      }

      .score-value {
        font-size: 24px;
        font-weight: bold;
        color: #2196F3;
      }

      .accuracy-value {
        font-size: 18px;
        font-weight: bold;
        color: #4CAF50;
      }
    }

    .duration-info {
      display: flex;
      flex-direction: column;
      align-items: center;

      .duration-label {
        font-size: 16px;
        color: #666;
        margin-bottom: 4px;
      }

      .duration-value {
        font-size: 18px;
        font-weight: bold;
        color: #FF9800;
      }
    }
  }

  .wrong-words {
    .wrong-words-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
      display: block;
    }

    .wrong-words-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .wrong-word-item {
      display: flex;
      gap: 15px;
      padding: 15px;
      background: #fff;
      border-radius: 10px;
      border: 1px solid #eee;

      .wrong-word-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        flex-shrink: 0;
        border: 1px solid #ddd;
      }

      .wrong-word-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .correct-answer {
          font-size: 16px;
          font-weight: bold;
          color: #2196F3;
        }

        .word-meaning {
          font-size: 16px;
          color: #333;
        }

        .user-answer {
          font-size: 16px;
          color: #f44336;
          font-style: italic;
        }

        .attempts-count {
          font-size: 14px;
          color: #999;
          background: #f5f5f5;
          padding: 2px 8px;
          border-radius: 8px;
          align-self: flex-start;
        }
      }
    }
  }
}