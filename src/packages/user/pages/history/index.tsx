import { Component } from 'react'
import { View, Text, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { GameHistory } from '../../../../types/game'
import { HistoryService } from '../../../../services/historyService'
import './index.scss'

interface HistoryPageState {
  historyList: GameHistory[]
  isLoading: boolean
}

export default class History extends Component<{}, HistoryPageState> {
  constructor(props) {
    super(props)
    this.state = {
      historyList: [],
      isLoading: true
    }

    this.loadHistory = this.loadHistory.bind(this)
    this.clearAllHistory = this.clearAllHistory.bind(this)
    this.goBack = this.goBack.bind(this)
  }

  componentDidMount() {
    this.loadHistory()
  }

  async loadHistory() {
    try {
      this.setState({ isLoading: true })
      const history = await HistoryService.getAllHistory()
      this.setState({ 
        historyList: history,
        isLoading: false 
      })
    } catch (error) {
      console.error('加载历史记录失败:', error)
      this.setState({ isLoading: false })
      Taro.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  }

  clearAllHistory() {
    Taro.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            await HistoryService.clearHistory()
            this.setState({ historyList: [] })
            Taro.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空历史记录失败:', error)
            Taro.showToast({
              title: '清空失败',
              icon: 'error'
            })
          }
        }
      }
    })
  }

  goBack() {
    Taro.navigateBack()
  }

  render() {
    const { historyList, isLoading } = this.state

    if (isLoading) {
      return (
        <View className='history-container'>
          <View className='loading-state'>
            <Text className='loading-text'>📊 正在加载历史记录...</Text>
          </View>
        </View>
      )
    }

    return (
      <View className='history-container'>
        <View className='header'>
          <Button className='back-btn' onClick={this.goBack}>
            ← 返回
          </Button>
          <Text className='page-title'>历史记录</Text>
          {historyList.length > 0 && (
            <Button className='clear-btn' onClick={this.clearAllHistory}>
              清空
            </Button>
          )}
        </View>

        {historyList.length === 0 ? (
          <View className='empty-state'>
            <Text className='empty-icon'>📝</Text>
            <Text className='empty-text'>暂无游戏记录</Text>
            <Text className='empty-tip'>完成一局游戏后，记录会出现在这里</Text>
          </View>
        ) : (
          <View className='history-list'>
            {historyList.map((record, index) => (
              <View key={record.id} className='history-item'>
                <View className='item-header'>
                  <View className='item-left'>
                    <Text className='item-number'>第 {historyList.length - index} 局</Text>
                    <Text 
                      className='game-mode-badge'
                      data-mode={record.gameMode}
                    >
                      {record.gameMode === 'pronunciation' ? '发音挑战' : '看图猜词'}
                    </Text>
                  </View>
                  <Text className='item-date'>
                    {HistoryService.formatDate(record.date)}
                  </Text>
                </View>

                <View className='score-info'>
                  <View className='score-main'>
                    <Text className='score-label'>得分</Text>
                    <Text className='score-value'>
                      {record.score}/{record.totalWords}
                    </Text>
                    <Text className='accuracy-value'>
                      ({record.accuracy}%)
                    </Text>
                  </View>
                  <View className='duration-info'>
                    <Text className='duration-label'>用时</Text>
                    <Text className='duration-value'>
                      {HistoryService.formatDuration(record.duration)}
                    </Text>
                  </View>
                </View>

                {record.wrongWords.length > 0 && (
                  <View className='wrong-words'>
                    <Text className='wrong-words-title'>
                      错误单词 ({record.wrongWords.length})
                    </Text>
                    <View className='wrong-words-list'>
                      {record.wrongWords.map((wrongWord, wordIndex) => (
                        <View key={wordIndex} className='wrong-word-item'>
                          <Image
                            className='wrong-word-image'
                            src={wrongWord.word.image}
                            mode='aspectFit'
                          />
                          <View className='wrong-word-details'>
                            <Text className='correct-answer'>
                              正确: {wrongWord.word.word}
                            </Text>
                            <Text className='word-meaning'>
                              释义: {wrongWord.word.meaning}
                            </Text>
                            <Text className='user-answer'>
                              你答: {wrongWord.userAnswer}
                            </Text>
                            <Text className='attempts-count'>
                              尝试: {wrongWord.attempts}{record.gameMode === 'pronunciation' ? '' : '/3'}
                            </Text>
                          </View>
                        </View>
                      ))}
                    </View>
                  </View>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    )
  }
}