import { Component } from 'react'
import { View, Text, Image, Canvas, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { ShareData } from '../../../../types/game'
import { ShareService } from '../../../../services/shareService'
import './index.scss'

interface SharePageState {
  shareData: ShareData | null
  canvasWidth: number
  canvasHeight: number
}

export default class Share extends Component<{}, SharePageState> {
  private shareImagePath: string = ''

  constructor(props: {}) {
    super(props)
    this.state = {
      shareData: {
        'score': 0,
        'duration': 0,
        'totalWords': 0,
        'accuracy': 0,
        wrongWords: [],
        correctWords: [],
        date: ''
      },
      canvasWidth: 340,
      canvasHeight: 580  // 减少高度，移除底部区域
    }

    this.generateCanvas = this.generateCanvas.bind(this)
    this.saveImage = this.saveImage.bind(this)
    this.prepareShareImage = this.prepareShareImage.bind(this)
    this.shareToMoments = this.shareToMoments.bind(this)
  }

  // 配置分享功能 - 这个方法会在点击 open-type="share" 的按钮时被调用
  async onShareAppMessage(options: any) {
    const { shareData } = this.state
    const isEndlessMode = shareData?.totalWords === 0

    // 如果是通过按钮触发的分享，准备分享图片
    if (options.from === 'button') {
      const imagePath = await this.prepareShareImage()
      const title = isEndlessMode
        ? `我在无尽挑战中答对了${shareData?.score}个单词，快来试试吧！`
        : `我在发音挑战中得了${shareData?.score}分，正确率${shareData?.accuracy}%，快来试试吧！`

      return {
        title,
        path: '/pages/index/index',
        imageUrl: imagePath || this.shareImagePath || ''
      }
    }

    const title = isEndlessMode
      ? `我在无尽挑战中答对了${shareData?.score}个单词，快来试试吧！`
      : `我在发音挑战中得了${shareData?.score}分，正确率${shareData?.accuracy}%，快来试试吧！`

    return {
      title,
      path: '/pages/index/index',
      imageUrl: this.shareImagePath || ''
    }
  }

  // 配置分享到朋友圈 - 用户通过右上角菜单分享时调用
  async onShareTimeline() {
    const { shareData } = this.state
    const isEndlessMode = shareData?.totalWords === 0

    // 如果还没有生成分享图片，先生成
    if (!this.shareImagePath) {
      await this.prepareShareImage()
    }

    const title = isEndlessMode
      ? `无尽挑战成绩：答对${shareData?.score}个单词`
      : `发音挑战成绩：${shareData?.score}分，正确率${shareData?.accuracy}%`

    return {
      title,
      imageUrl: this.shareImagePath || '',
      query: '' // 可以添加参数，用于跟踪分享来源
    }
  }

  componentDidMount() {
    const instance = Taro.getCurrentInstance()
    const { data } = instance.router?.params || {}

    console.log('分享页面接收到的原始数据:', data)

    if (data) {
      try {
        const shareData: ShareData = JSON.parse(decodeURIComponent(data))
        console.log('解析后的分享数据:', shareData)
        this.setState({ shareData })
      } catch (error) {
        console.error('解析分享数据失败:', error)
        Taro.showToast({
          title: '数据解析失败',
          icon: 'error'
        })
      }
    } else {
      console.error('没有接收到分享数据')
      Taro.showToast({
        title: '没有分享数据',
        icon: 'error'
      })
    }

    // 启用分享菜单，包括分享到朋友圈
    Taro.showShareMenu({
      withShareTicket: true
    })

    // 等待state更新完成后生成Canvas
    setTimeout(() => {
      if (this.state.shareData) {
        this.generateCanvas()
      }
    }, 200)
  }

  async generateCanvas() {
    const { shareData } = this.state
    if (!shareData) return

    try {
      const ctx = Taro.createCanvasContext('shareCanvas', this)
      const { canvasWidth, canvasHeight } = this.state

      console.log('开始绘制Canvas，尺寸:', canvasWidth, canvasHeight)

      // 绘制新的分享背景图
      try {
        const shareImagePath = '/assets/share-bg.png'
        ctx.drawImage(shareImagePath, 0, 0, canvasWidth, canvasHeight)
        console.log('分享背景图绘制成功')
      } catch (error) {
        console.warn('分享背景图加载失败，使用降级背景', error)
        // 降级方案：使用渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
        gradient.addColorStop(0, '#FFB6C1')
        gradient.addColorStop(0.3, '#DDA0DD')
        gradient.addColorStop(0.7, '#87CEEB')
        gradient.addColorStop(1, '#98FB98')
        ctx.setFillStyle(gradient)
        ctx.fillRect(0, 0, canvasWidth, canvasHeight)
      }

      // 绘制小程序二维码 /assets/mp-icon.png
      try {
        const shareImageQRPath = '/assets/mp-icon.png'
        const qrSize = 60
        const qrX = canvasWidth - qrSize - 10
        const qrY = 40
        ctx.drawImage(shareImageQRPath, qrX, qrY, qrSize, qrSize)
        console.log('分享背景图绘制成功')
      } catch (error) {
      }

      // 在白色框中填入答对题数和用时
      // 注意：以下位置需要根据实际的 share-bg.png 中白色框的位置进行调整
      const dataY = 190  // 垂直位置，根据分享图中白色框的Y坐标调整
      const leftX = canvasWidth / 2 - 60   // 左侧数字位置（答对题数）
      const rightX = canvasWidth / 2 + 60  // 右侧数字位置（挑战用时）

      // 设置文字样式
      ctx.setFillStyle('#FF4500')  // 橙红色，可根据设计要求调整
      ctx.setFontSize(36)          // 字体大小，可根据白色框大小调整
      ctx.setTextAlign('center')

      // 绘制答对题数
      ctx.fillText(`${shareData.score}`, leftX, dataY)

      // 绘制挑战用时
      ctx.fillText(`${ShareService.formatDuration(shareData.duration)}`, rightX, dataY)
      // 绘制文字说明

      ctx.setFillStyle('black')  // 橙红色，可根据设计要求调整
      ctx.setFontSize(18)          // 字体大小，可根据白色框大小调整
      ctx.fillText("答对题数", leftX, dataY + 40)
      ctx.fillText("挑战用时", rightX, dataY + 40)

      // 一次性绘制所有内容
      ctx.draw(true, () => {
        console.log('Canvas绘制完成')
      })

    } catch (error) {
      console.error('生成canvas失败:', error)
      Taro.showToast({
        title: '生成失败',
        icon: 'error'
      })
    }
  }

  async saveImage() {
    try {
      const { canvasWidth, canvasHeight } = this.state

      const result = await Taro.canvasToTempFilePath({
        canvasId: 'shareCanvas',
        width: canvasWidth,
        height: canvasHeight,
        destWidth: canvasWidth * 2,
        destHeight: canvasHeight * 2
      })

      await Taro.saveImageToPhotosAlbum({
        filePath: result.tempFilePath
      })

      Taro.showModal({
        title: '保存成功',
        content: '分享图已保存到相册，快去分享给朋友吧！',
        showCancel: false,
        confirmText: '好的'
      })

    } catch (error) {
      console.error('保存图片失败:', error)
      if (error.errMsg && error.errMsg.includes('auth deny')) {
        Taro.showModal({
          title: '需要相册权限',
          content: '保存图片需要相册权限，请在设置中开启',
          showCancel: false
        })
      } else {
        Taro.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    }
  }

  async prepareShareImage() {
    try {
      const { canvasWidth, canvasHeight } = this.state

      const result = await Taro.canvasToTempFilePath({
        canvasId: 'shareCanvas',
        width: canvasWidth,
        height: canvasHeight,
        destWidth: canvasWidth * 2,
        destHeight: canvasHeight * 2
      })

      // 保存分享图片路径到实例变量
      this.shareImagePath = result.tempFilePath
      return result.tempFilePath

    } catch (error) {
      console.error('准备分享图片失败:', error)
      Taro.showToast({
        title: '准备分享图片失败',
        icon: 'error'
      })
      return ''
    }
  }

  async shareToMoments() {
    try {
      const { canvasWidth, canvasHeight } = this.state

      // 先显示加载提示
      Taro.showLoading({
        title: '正在生成海报...'
      })

      const result = await Taro.canvasToTempFilePath({
        canvasId: 'shareCanvas',
        width: canvasWidth,
        height: canvasHeight,
        destWidth: canvasWidth * 2,
        destHeight: canvasHeight * 2
      })

      // 自动保存图片到相册
      await Taro.saveImageToPhotosAlbum({
        filePath: result.tempFilePath
      })

      Taro.hideLoading()

      // 保存成功后的提示
      Taro.showModal({
        title: '海报已保存',
        content: '图片已保存到相册\n您可以去微信朋友圈分享这张图片啦！',
        confirmText: '去分享',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            // 用户点击去分享，可以引导打开相册或给出更详细的提示
            Taro.showToast({
              title: '请在朋友圈选择刚保存的图片',
              icon: 'none',
              duration: 3000
            })
          }
        }
      })

    } catch (error) {
      Taro.hideLoading()
      console.error('保存图片失败:', error)

      if (error.errMsg && error.errMsg.includes('auth deny')) {
        // 没有相册权限
        Taro.showModal({
          title: '需要授权',
          content: '保存图片需要您的相册权限，请在设置中开启',
          confirmText: '去设置',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting()
            }
          }
        })
      } else {
        Taro.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      }
    }
  }



  render() {
    const { shareData } = this.state

    if (!shareData) {
      return (
        <View className='share-container'>
          <View className='loading-state'>
            <Text className='loading-text'>数据加载中...</Text>
          </View>
        </View>
      )
    }

    return (
      <View className='share-container'>
        <View className='canvas-section'>
          <Canvas
            canvasId='shareCanvas'
            className='share-canvas'
            style={{
              width: `${this.state.canvasWidth}px`,
              height: `${this.state.canvasHeight}px`
            }}
          />
        </View>

        {/* 固定在底部的分享按钮 */}
        <View className='share-buttons-fixed'>
          <Button
            className='share-btn friend-btn'
            openType='share'
          >
            <View className='btn-content'>
              <View className='btn-icon'>👥</View>
              <Text className='btn-text'>分享给朋友</Text>
            </View>
          </Button>

          <Button
            className='share-btn moments-btn'
            onClick={this.shareToMoments}
          >
            <View className='btn-content'>
              <View className='btn-icon'>🌟</View>
              <Text className='btn-text'>分享到朋友圈</Text>
            </View>
          </Button>

          <Button
            className='share-btn save-btn'
            onClick={this.saveImage}
          >
            <View className='btn-content'>
              <View className='btn-icon'>💾</View>
              <Text className='btn-text'>保存到相册</Text>
            </View>
          </Button>
        </View>
      </View>
    )
  }
}
