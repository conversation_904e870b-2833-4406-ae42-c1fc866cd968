/* 强制覆盖页面背景，包括安全区域 */
page {
  background: #000000 !important;
  background-color: #000000 !important;
}

.share-container {
  min-height: 100vh;
  background: #000000 !important; // 强制黑色背景
  padding: 20px 20px 120px 20px;
  box-sizing: border-box;
  overflow: hidden; // 防止页面滚动
  display: flex;
  flex-direction: column;
  position: relative;
}

// Header样式已移除，因为不再使用header区域

.loading-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 100px;

  .loading-text {
    font-size: 24px;
    color: white;
    font-weight: bold;
    text-align: center;
  }
}

.preview-section {
  margin-bottom: 30px;

  .preview-card {
    background: rgba(255, 255, 255, 0.639);
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);

    .card-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 25px;
      display: block;
    }

    .score-preview {
      .performance-text {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        display: block;
      }

      .score-text {
        font-size: 48px;
        font-weight: bold;
        margin-bottom: 15px;
        display: block;
      }

      .accuracy-text {
        font-size: 20px;
        color: #666;
        margin-bottom: 10px;
        display: block;
      }

      .duration-text {
        font-size: 18px;
        color: #999;
        margin-bottom: 15px;
        display: block;
      }
    }

    .wrong-words-count {
      font-size: 16px;
      color: #f44336;
      padding: 8px 16px;
      background: #ffebee;
      border-radius: 20px;
      display: inline-block;
    }
  }
}

.canvas-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  .share-canvas {
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    display: block;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

// 固定在底部的分享按钮
.share-buttons-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 15px 20px;
  padding-bottom: calc(15px + env(safe-area-inset-bottom)); // 适配iPhone底部安全区域
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);

  .share-btn {
    background: transparent;
    border: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.2s ease;
    flex: 1;

    .btn-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;

      .btn-icon {
        width: 44px;
        height: 44px;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;
      }

      .btn-text {
        font-size: 12px;
        color: white;
        font-weight: 500;
        text-align: center;
        line-height: 1.2;
      }
    }

    &.friend-btn .btn-icon {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
    }

    &.moments-btn .btn-icon {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
    }

    &.save-btn .btn-icon {
      background: linear-gradient(135deg, #FF9800, #F57C00);
      color: white;
    }

    &:active {
      transform: scale(0.95);

      .btn-icon {
        transform: scale(0.9);
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
      }
    }
  }
}
