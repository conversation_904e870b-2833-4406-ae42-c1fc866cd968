import { <PERSON>, <PERSON>, <PERSON><PERSON>, ScrollView } from '@tarojs/components'
import { Component } from 'react'
import Taro from '@tarojs/taro'
import { userService } from '../../../../services/userService'
import { rankingService, type RankingType, type RankingItem, type RankingResponse } from '../../../../services/rankingService'
import type { UserInfo } from '../../../../types/user'
import './index.scss'

type State = {
  userInfo: UserInfo | null
  currentTab: RankingType
  rankings: RankingItem[]
  userRank?: number
  userScore?: number
  totalCount?: number
  loading: boolean
}

export default class Ranking extends Component<{}, State> {
  constructor(props) {
    super(props)
    this.state = {
      userInfo: null,
      currentTab: 'guess',
      rankings: [],
      userRank: undefined,
      userScore: undefined,
      totalCount: undefined,
      loading: false
    }
  }

  componentDidMount() {
    this.loadUserInfo()
    this.loadRankings()
  }

  loadUserInfo = () => {
    const userInfo = userService.getUserInfo()
    this.setState({ userInfo })
  }

  handleLogin = async () => {
    try {
      Taro.showLoading({ title: '登录中...' })
      const userInfo = await userService.login()
      this.setState({ userInfo })
      Taro.hideLoading()
      Taro.showToast({
        title: '登录成功',
        icon: 'success'
      })
      this.loadRankings()
    } catch (error) {
      Taro.hideLoading()
      Taro.showToast({
        title: error.message || '登录失败',
        icon: 'error'
      })
    }
  }

  loadRankings = async () => {
    try {
      this.setState({ loading: true })
      const response = await rankingService.getRankingList(this.state.currentTab)
      this.setState({ 
        rankings: response.list,
        userRank: response.userRank,
        userScore: response.userScore,
        totalCount: response.totalCount
      })
    } catch (error) {
      Taro.showToast({
        title: '加载排行榜失败',
        icon: 'error'
      })
    } finally {
      this.setState({ loading: false })
    }
  }

  handleTabChange = (tab: RankingType) => {
    this.setState({ currentTab: tab }, () => {
      this.loadRankings()
    })
  }

  goToProfile = () => {
    // 已登录时点击按钮，可以跳转到个人中心或显示用户菜单
    Taro.showToast({
      title: '个人中心功能待开发',
      icon: 'none'
    })
  }

  getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇'
      case 2:
        return '🥈'
      case 3:
        return '🥉'
      default:
        return rank.toString()
    }
  }

  getTabTitle = (tab: RankingType) => {
    switch (tab) {
      case 'guess':
        return '猜单词'
      case 'pronunciation':
        return '发音挑战'
      case 'weekly':
        return '本周次数'
      case 'total':
        return '总次数'
      default:
        return ''
    }
  }

  render() {
    const { userInfo, currentTab, rankings } = this.state
    const isLoggedIn = userService.isLoggedIn()

    return (
      <View className='ranking'>
        {/* 头部标题和登录按钮 */}
        <View className='header'>
          <Text className='title'>排行榜</Text>
          <Button className='login-btn' onClick={isLoggedIn ? this.goToProfile : this.handleLogin}>
            {isLoggedIn ? '我的' : '登录'}
          </Button>
        </View>

        {/* Tab 切换 */}
        <View className='tabs'>
          {(['guess', 'pronunciation', 'weekly', 'total'] as RankingType[]).map(tab => (
            <View
              key={tab}
              className={`tab-item ${currentTab === tab ? 'active' : ''}`}
              onClick={() => this.handleTabChange(tab)}
            >
              <Text className='tab-text'>{this.getTabTitle(tab)}</Text>
            </View>
          ))}
        </View>

        {/* 排行榜内容 */}
        <View className='content'>
          {!isLoggedIn ? (
            /* 未登录蒙板 */
            <View className='login-mask'>
              <View className='mask-content'>
                <Text className='mask-icon'>🔒</Text>
                <Text className='mask-title'>请先登录</Text>
                <Text className='mask-desc'>登录后查看完整排行榜</Text>
                <Button className='mask-login-btn' onClick={this.handleLogin}>
                  立即登录
                </Button>
              </View>
            </View>
          ) : (
            /* 排行榜列表 */
            <ScrollView className='ranking-list' scrollY>
              {rankings.map(item => (
                <View 
                  key={item.id} 
                  className={`ranking-item ${item.isCurrentUser ? 'current-user' : ''}`}
                >
                  <View className='rank-column'>
                    <Text className={`rank-text ${item.isCurrentUser ? 'current-rank' : ''}`}>
                      {this.getRankIcon(item.rank)}
                    </Text>
                  </View>
                  <View className='user-column'>
                    <Text className={`user-name ${item.isCurrentUser ? 'current-name' : ''}`}>
                      {item.userName}
                      {item.isCurrentUser && <Text className='current-label'> (我)</Text>}
                    </Text>
                  </View>
                  <View className='score-column'>
                    <Text className={`score-text ${item.isCurrentUser ? 'current-score' : ''}`}>
                      {item.score}
                    </Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          )}
        </View>
      </View>
    )
  }
}
