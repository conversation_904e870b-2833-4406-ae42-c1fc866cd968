/* 覆盖页面背景到安全区域 */
page {
  background: linear-gradient(to bottom, #74b9ff 0%, #0984e3 70%, #FFFFFF 100%) !important;
}

.ranking {
  padding: calc(env(safe-area-inset-top) + 40rpx) 40rpx calc(env(safe-area-inset-bottom) + 40rpx);
  min-height: 100vh;
  display: flex;
  background: linear-gradient(to bottom, #74b9ff 0%, #0984e3 70%, #FFFFFF 100%);
  flex-direction: column;
  position: relative;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 0 60rpx;
    position: relative;

    .title {
      font-size: 60rpx;
      font-weight: 700;
      color: white;
      text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .login-btn {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2rpx solid rgba(255, 255, 255, 0.5);
      border-radius: 60rpx;
      padding: 16rpx 32rpx;
      font-size: 24rpx;
      font-weight: 600;
      backdrop-filter: blur(10rpx);
      min-width: 120rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &::after {
        border: none;
      }

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }

  .tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 100rpx;
    padding: 12rpx;
    margin-bottom: 60rpx;
    backdrop-filter: blur(20rpx);
    border: 2rpx solid rgba(255, 255, 255, 0.2);
    position: relative;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 32rpx 24rpx;
      border-radius: 88rpx;
      transition: all 0.3s ease;

      &.active {
        background: white;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

        .tab-text {
          color: #667eea;
          font-weight: 700;
        }
      }

      .tab-text {
        font-size: 26rpx;
        color: white;
        opacity: 0.9;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }
    }
  }

  .content {
    flex: 1;
    background: white;
    border-radius: 40rpx 40rpx 0 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
    margin-bottom: calc(env(safe-area-inset-bottom));

    .login-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.96);
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      .mask-content {
        text-align: center;
        padding: 120rpx 80rpx;

        .mask-icon {
          font-size: 200rpx;
          margin-bottom: 60rpx;
          display: block;
          opacity: 0.8;
        }

        .mask-title {
          font-size: 72rpx;
          font-weight: 700;
          color: #333;
          margin-bottom: 40rpx;
          display: block;
        }

        .mask-desc {
          font-size: 36rpx;
          color: #666;
          margin-bottom: 100rpx;
          display: block;
          line-height: 1.4;
        }

        .mask-login-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          border-radius: 100rpx;
          padding: 36rpx 80rpx;
          font-size: 36rpx;
          font-weight: 600;
          box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.3);

          &::after {
            border: none;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .ranking-list {
      height: 70vh;
      padding: 60rpx 0;

      .ranking-item {
        display: flex;
        align-items: center;
        padding: 40rpx 60rpx;
        border-bottom: 2rpx solid #f8f9fa;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        // 当前用户高亮样式
        &.current-user {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
          border: 2rpx solid rgba(102, 126, 234, 0.2);
          border-radius: 24rpx;
          box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
          margin: 8rpx 20rpx;
          padding: 40rpx 40rpx;

          &:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 100%);
          }
        }

        .rank-column {
          width: 120rpx;
          text-align: center;

          .rank-text {
            font-size: 64rpx;
            font-weight: 700;
            line-height: 1;

            &.current-rank {
              color: #667eea;
              text-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
            }
          }
        }

        .user-column {
          flex: 1;
          margin-left: 40rpx;

          .user-name {
            font-size: 36rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.2;

            &.current-name {
              color: #667eea;
              font-weight: 600;
            }

            .current-label {
              color: #667eea;
              font-weight: 700;
              font-size: 28rpx;
              background: rgba(102, 126, 234, 0.1);
              padding: 4rpx 12rpx;
              border-radius: 16rpx;
              margin-left: 8rpx;
            }
          }
        }

        .score-column {
          width: 160rpx;
          text-align: right;

          .score-text {
            font-size: 36rpx;
            color: #667eea;
            font-weight: 600;

            &.current-score {
              color: #667eea;
              font-weight: 700;
              font-size: 40rpx;
              text-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
            }
          }
        }
      }
    }
  }
}