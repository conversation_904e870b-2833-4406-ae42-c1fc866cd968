import { Component } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

export default class EndlessMode extends Component {

  componentDidMount() {
    // 启用分享功能
    Taro.showShareMenu({
      withShareTicket: true
    })
  }

  // 看图猜词模式
  startGuessMode = () => {
    Taro.navigateTo({
      url: '/pages/game/index?mode=endless-guess'
    })
  }

  // 发音挑战模式
  startPronunciationMode = () => {
    Taro.navigateTo({
      url: '/pages/game/index?mode=endless-pronunciation'
    })
  }

  // 返回首页
  goHome = () => {
    Taro.navigateBack()
  }

  // 配置分享功能
  onShareAppMessage() {
    return {
      title: '无尽英语挑战 - 提升口语能力',
      path: '/pages/index/index'
    }
  }

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '无尽英语挑战 - 提升口语能力'
    }
  }

  render() {
    return (
      <View className='endless-mode-container'>
        {/* 顶部装饰 */}
        <View className='decoration-top'>
          <View className='star star-1'>⭐</View>
          <View className='star star-2'>✨</View>
          <View className='star star-3'>🌟</View>
        </View>

        {/* 标题区域 */}
        <View className='header-section'>
          <Text className='main-title'>🎯 无尽挑战</Text>
          <Text className='subtitle'>选择你的挑战模式</Text>
          <Text className='description'>
            看看能说对多少个单词
          </Text>
        </View>

        {/* 模式选择区域 */}
        <View className='modes-section'>
          {/* 看图猜词模式 */}
          <View className='mode-card guess-mode'>
            <View className='mode-icon'>
              <Text className='icon-emoji'>🖼️</Text>
            </View>
            <View className='mode-content'>
              <Text className='mode-title'>看图猜词</Text>
              <Text className='mode-description'>
                看图片和解释
                {'\n'}
                说出对应的英文单词
              </Text>
              <View className='mode-features'>
                <Text className='feature-item'>• 锻炼词汇联想能力</Text>
                <Text className='feature-item'>• 提升单词记忆</Text>
                <Text className='feature-item'>• 培养英语思维</Text>
              </View>
            </View>
            <Button
              className='mode-btn guess-btn'
              onClick={this.startGuessMode}
            >
              开始猜词挑战
            </Button>
          </View>

          {/* 发音挑战模式 */}
          <View className='mode-card pronunciation-mode'>
            <View className='mode-icon'>
              <Text className='icon-emoji'>🗣️</Text>
            </View>
            <View className='mode-content'>
              <Text className='mode-title'>发音挑战</Text>
              <Text className='mode-description'>
                看图片和英文单词
                {'\n'}
                练习正确发音
              </Text>
              <View className='mode-features'>
                <Text className='feature-item'>• 提升发音准确度</Text>
                <Text className='feature-item'>• 加强口语练习</Text>
                <Text className='feature-item'>• 培养语感</Text>
              </View>
            </View>
            <Button
              className='mode-btn pronunciation-btn'
              onClick={this.startPronunciationMode}
            >
              开始发音挑战
            </Button>
          </View>
        </View>

        {/* 游戏规则说明 */}
        <View className='rules-section'>
          <Text className='rules-title'>🎮 游戏规则</Text>
          <View className='rules-content'>
            <Text className='rule-item'>💖 初始5颗心</Text>
            <Text className='rule-item'>❌ 答错扣0.5颗心</Text>
            <Text className='rule-item'>⏭️ 跳过扣1颗心</Text>
            <Text className='rule-item'>🔥 连续答对5题+1颗心</Text>
            <Text className='rule-item'>💀 ❤️耗尽游戏结束</Text>
          </View>
        </View>

        {/* 底部按钮 */}
        <View className='footer-section'>
          <Button className='back-btn' onClick={this.goHome}>
            返回首页
          </Button>
        </View>

        {/* 底部装饰 */}
        <View className='decoration-bottom'>
          <View className='wave wave-1'></View>
          <View className='wave wave-2'></View>
        </View>
      </View>
    )
  }
}
