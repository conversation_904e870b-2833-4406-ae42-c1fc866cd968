/* 自定义导航栏模式下覆盖页面背景 */
page {
  background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 50%, #1E3A8A 100%) !important;
}

.endless-mode-container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 50%, #1E3A8A 100%);
  position: relative;
  padding: 40rpx 30rpx 60rpx;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* 顶部装饰 */
.decoration-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  z-index: 1;

  .star {
    position: absolute;
    font-size: 32rpx;
    animation: twinkle 2s infinite ease-in-out;

    &.star-1 {
      top: 60rpx;
      left: 60rpx;
      animation-delay: 0s;
    }

    &.star-2 {
      top: 100rpx;
      right: 80rpx;
      animation-delay: 0.6s;
    }

    &.star-3 {
      top: 140rpx;
      left: 50%;
      transform: translateX(-50%);
      animation-delay: 1.2s;
    }
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-top: 80rpx;
  margin-bottom: 60rpx;
  z-index: 2;

  .main-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff !important;
    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
    display: block;
    margin-bottom: 20rpx;
  }

  .subtitle {
    font-size: 32rpx;
    color: #ffffff !important;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    display: block;
    margin-bottom: 30rpx;
  }

  .description {
    font-size: 28rpx;
    color: #ffffff !important;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    line-height: 1.6;
    display: block;
  }
}

/* 模式选择区域 */
.modes-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  margin-bottom: 40rpx;
  z-index: 2;
}

.mode-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #4CAF50, #45a049);
  }

  &.pronunciation-mode::before {
    background: linear-gradient(90deg, #2196F3, #1976D2);
  }

  .mode-icon {
    text-align: center;
    margin-bottom: 30rpx;

    .icon-emoji {
      font-size: 80rpx;
      display: block;
    }
  }

  .mode-content {
    text-align: center;
    margin-bottom: 40rpx;

    .mode-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 20rpx;
    }

    .mode-description {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      display: block;
      margin-bottom: 30rpx;
    }

    .mode-features {
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .feature-item {
        font-size: 24rpx;
        color: #777;
        text-align: left;
      }
    }
  }

  .mode-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    color: white;

    &.guess-btn {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
    }

    &.pronunciation-btn {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.4);
    }

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
  }
}

/* 游戏规则说明 */
.rules-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  .rules-title {
    font-size: 30rpx;
    font-weight: bold;
    color: white;
    text-align: center;
    display: block;
    margin-bottom: 25rpx;
  }

  .rules-content {
    display: flex;
    flex-direction: column;
    gap: 15rpx;

    .rule-item {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.9);
      padding-left: 10rpx;
    }
  }
}

/* 底部按钮 */
.footer-section {
  z-index: 2;

  .back-btn {
    width: 100%;
    height: 88rpx;
    background: rgba(255, 255, 255, 0.15);
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 44rpx;
    font-size: 32rpx;
    color: white;
    backdrop-filter: blur(10rpx);

    &:active {
      background: rgba(255, 255, 255, 0.25);
    }
  }
}

/* 底部装饰 */
.decoration-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  overflow: hidden;

  .wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: wave 4s infinite linear;

    &.wave-1 {
      animation-delay: 0s;
    }

    &.wave-2 {
      background: rgba(255, 255, 255, 0.05);
      animation-delay: 2s;
      animation-duration: 6s;
    }
  }
}

@keyframes wave {
  0% { transform: translateX(-50%) translateY(20rpx); }
  100% { transform: translateX(-50%) translateY(20rpx) rotateZ(360deg); }
}