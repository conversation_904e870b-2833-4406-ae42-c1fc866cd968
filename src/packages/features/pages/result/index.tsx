import { Component } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { WrongWordResult, Word } from '../../../../types/game'
import { ShareService } from '../../../../services/shareService'
import { audioService } from '../../../../services/audioService'
import { userService } from '../../../../services/userService'
import { CustomerService } from '../../../../services/customerService'
import './index.scss'

interface ResultState {
  score: number
  totalWords: number
  duration: number
  wrongWords: WrongWordResult[]
  correctWords: Word[]
  showShareInfo: boolean
  gameMode: 'guess' | 'pronunciation' | 'endless' | 'endless-guess' | 'endless-pronunciation'
}

export default class Result extends Component<{}, ResultState> {
  constructor(props) {
    super(props)
    this.state = {
      score: 0,
      totalWords: 5,
      duration: 0,
      wrongWords: [],
      correctWords: [],
      showShareInfo: false,
      gameMode: 'guess'
    }
  }

  componentDidMount() {
    // 播放完成庆祝音效
    audioService.playCompletionSound().catch(error => {
      console.warn('播放完成音效失败:', error)
    })

    // 从URL参数获取数据
    const instance = Taro.getCurrentInstance()
    const { score = '0', total = '5', duration = '0', wrongWords = '[]', correctWords = '[]', mode = 'guess' } = instance.router?.params || {}
    
    console.log('Result页面接收到的参数:', { score, total, duration, mode })

    let parsedWrongWords: WrongWordResult[] = []
    let parsedCorrectWords: Word[] = []
    try {
      parsedWrongWords = JSON.parse(decodeURIComponent(wrongWords))
    } catch (error) {
      console.error('解析错误单词数据失败:', error)
    }

    try {
      parsedCorrectWords = JSON.parse(decodeURIComponent(correctWords))
    } catch (error) {
      console.error('解析正确单词数据失败:', error)
    }

    this.setState({
      score: parseInt(score),
      totalWords: parseInt(total),
      duration: parseInt(duration),
      wrongWords: parsedWrongWords,
      correctWords: parsedCorrectWords,
      gameMode: mode as any
    })

    // 提交游戏结果（如果用户已登录）
    this.submitGameResult({
      score: parseInt(score),
      totalWords: parseInt(total),
      duration: parseInt(duration),
      wrongWords: parsedWrongWords,
      gameMode: mode as any
    })

    // 启用分享功能
    Taro.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  }

  // 提交游戏结果
  submitGameResult = async (gameData: {
    score: number
    totalWords: number
    duration: number
    wrongWords: WrongWordResult[]
    gameMode: 'guess' | 'pronunciation' | 'endless' | 'endless_guess' | 'endless_challenge'
  }) => {
    if (!userService.isLoggedIn()) {
      console.log('User not logged in, skipping game result submission')
      return
    }

    try {
      const { score, totalWords, duration, wrongWords, gameMode } = gameData
      const accuracy = totalWords > 0 ? (score / totalWords) * 100 : 0
      
      console.log('准备提交游戏结果:', { score, totalWords, duration, gameMode, accuracy })

      await userService.submitGameResult({
        score,
        totalWords,
        accuracy,
        duration,
        wrongWords: wrongWords.map(w => ({
          word: w.word.word,
          userAnswer: w.userAnswer,
          attempts: w.attempts
        })),
        completedAt: Date.now(),
        gameMode
      })

      console.log('Game result submitted successfully')
    } catch (error) {
      console.error('Failed to submit game result:', error)
    }
  }

  // 配置分享到对话功能
  onShareAppMessage() {
    const { score, totalWords, duration } = this.state
    const durationText = this.formatDuration(duration)

    // 检查是否是无尽模式
    const isEndlessMode = totalWords === 0

    if (isEndlessMode) {
      return {
        title: `我在无尽挑战中答对了${score}个单词，用时${durationText}`,
        path: '/pages/index/index'
      }
    } else {
      const percentage = Math.round((score / totalWords) * 100)
      return {
        title: `我在发音挑战中得了${score}分，正确率${percentage}%，用时${durationText}`,
        path: '/pages/index/index'
      }
    }
  }

  // 配置分享到朋友圈功能
  onShareTimeline() {
    const { score, totalWords, duration } = this.state
    const durationText = this.formatDuration(duration)

    // 检查是否是无尽模式
    const isEndlessMode = totalWords === 0

    if (isEndlessMode) {
      return {
        title: `无尽挑战成绩：答对${score}个单词，用时${durationText}`
      }
    } else {
      const percentage = Math.round((score / totalWords) * 100)
      return {
        title: `发音挑战成绩：${score}/${totalWords}分，正确率${percentage}%，用时${durationText}`
      }
    }
  }

  // 格式化时间显示
  formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60

    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`
    } else {
      return `${remainingSeconds}秒`
    }
  }

  // 获取成绩评价
  getPerformanceText = (score: number, total: number): { text: string; emoji: string; color: string } => {
    // 检查是否是无尽模式
    const isEndlessMode = total === 0

    if (isEndlessMode) {
      // 无尽模式根据答对数量评价
      if (score >= 50) {
        return { text: '传奇表现！', emoji: '👑', color: '#FFD700' }
      } else if (score >= 30) {
        return { text: '大师级别！', emoji: '🏆', color: '#FF6B35' }
      } else if (score >= 15) {
        return { text: '表现优秀！', emoji: '🎉', color: '#4CAF50' }
      } else if (score >= 5) {
        return { text: '继续加油！', emoji: '👍', color: '#2196F3' }
      } else {
        return { text: '多多练习！', emoji: '💪', color: '#9C27B0' }
      }
    } else {
      // 原有模式的评价逻辑
      const percentage = (score / total) * 100

      if (percentage === 100) {
        return { text: '完美表现！', emoji: '🏆', color: '#FFD700' }
      } else if (percentage >= 80) {
        return { text: '表现优秀！', emoji: '🎉', color: '#FF6B35' }
      } else if (percentage >= 60) {
        return { text: '继续加油！', emoji: '👍', color: '#4CAF50' }
      } else {
        return { text: '多多练习！', emoji: '💪', color: '#2196F3' }
      }
    }
  }

  restartGame = () => {
    Taro.redirectTo({
      url: '/pages/game/index'
    })
  }

  goHome = () => {
    Taro.redirectTo({
      url: '/pages/index/index'
    })
  }

  shareToWechat = () => {
    const { score, totalWords, duration } = this.state
    const isEndlessMode = totalWords === 0
    const durationText = this.formatDuration(duration)
    const performance = this.getPerformanceText(score, totalWords)

    // 生成分享内容
    const shareContent = isEndlessMode
      ? `我在英语单词无尽挑战中答对了 ${score} 个单词，用时 ${durationText}！${performance.text} 快来挑战吧！`
      : `我在英语单词发音游戏中获得了 ${score}/${totalWords} 的成绩，用时 ${durationText}！${performance.text} 快来挑战吧！`

    // 复制到剪贴板
    Taro.setClipboardData({
      data: shareContent,
      success: () => {
        Taro.showToast({
          title: '成绩已复制到剪贴板',
          icon: 'success',
          duration: 2000
        })

        // 显示分享提示
        setTimeout(() => {
          Taro.showModal({
            title: '分享到微信',
            content: '成绩内容已复制，请打开微信粘贴分享给好友',
            showCancel: false,
            confirmText: '我知道了'
          })
        }, 2000)
      }
    })
  }

  generateShareImage = async () => {
    try {
      Taro.showToast({
        title: '正在生成分享图...',
        icon: 'loading',
        duration: 2000
      })

      const { score, totalWords, duration, wrongWords, correctWords } = this.state
      const shareData = ShareService.generateShareData(score, totalWords, duration, wrongWords, correctWords)
      await ShareService.navigateToShare(shareData)

    } catch (error) {
      console.error('生成分享图失败:', error)
      Taro.showToast({
        title: '生成失败',
        icon: 'error'
      })
    }
  }

  // 播放单词读音
  playWordPronunciation = (word: string) => {
    audioService.playWordPronunciation(word).catch(error => {
      console.warn('播放单词读音失败:', error)
      Taro.showToast({
        title: '音频播放失败',
        icon: 'none',
        duration: 2000
      })
    })
  }

  openKfChat = () => {
    CustomerService.openKfChat()
  }

  render() {
    const { score, totalWords, duration, wrongWords, showShareInfo } = this.state
    const isEndlessMode = totalWords === 0
    const percentage = isEndlessMode ? 0 : Math.round((score / totalWords) * 100)
    const performance = this.getPerformanceText(score, totalWords)
    const durationText = this.formatDuration(duration)

    return (
      <View className='result-container'>
        {/* 顶部装饰 */}
        <View className='decoration-top'>
          <View className='star star-1'>⭐</View>
          <View className='star star-2'>✨</View>
          <View className='star star-3'>🌟</View>
        </View>

        {/* 主要内容区域 */}
        <View className='content-wrapper'>
          {/* 恭喜区域 */}
          <View className='celebration-section'>
            <View className='title-row'>
              <View className='trophy-container'>
                <Text className='trophy-emoji' style={{ color: performance.color }}>
                  {performance.emoji}
                </Text>
              </View>
              <Text className='congratulations-title'>恭喜完成！</Text>
            </View>
            <Text className='performance-text' style={{ color: performance.color }}>
              {performance.text}
            </Text>
          </View>

          {/* 成绩展示区域 */}
          <View className='score-section'>
            <View className='score-card'>
              {/* 突出显示总时间 */}
              <View className='time-highlight'>
                <Text className='time-highlight-label'>总用时</Text>
                <Text className='time-highlight-value'>{durationText}</Text>
              </View>

              <View className='score-main'>
                {/*<Text className='score-label'>{isEndlessMode ? '答对单词' : '你的得分'}</Text>*/}
                <View className='score-display'>
                  <Text className='score-number'>{score}</Text>
                  {!isEndlessMode && (
                    <>
                      <Text className='score-divider'>/</Text>
                      <Text className='score-total'>{totalWords}</Text>
                    </>
                  )}
                  {isEndlessMode && <Text className='score-unit'>个</Text>}
                </View>
              </View>
            </View>
          </View>

          {/* 错误单词回顾区域 */}
          {this.state.wrongWords.length > 0 && (
            <View className='wrong-words-section'>
              <Text className='section-title'>错误单词回顾</Text>
              <Text className='section-subtitle'>复习这些单词，下次会更好！</Text>

              <View className='wrong-words-list'>
                {this.state.wrongWords.map((wrongWord, index) => (
                  <View key={index} className='wrong-word-item'>
                    <View className='wrong-word-header'>
                      <Text className='wrong-word-number'>#{index + 1}</Text>
                      <Text className='attempts-info'>尝试了 {wrongWord.attempts} 次</Text>
                    </View>

                    <View className='wrong-word-content'>
                      <View className='word-image-container'>
                        <Image
                          className='word-image'
                          src={wrongWord.word.image}
                          mode='aspectFit'
                        />
                        <Text className='word-image-caption'>{wrongWord.word.meaning}</Text>
                      </View>

                      <View className='word-details'>
                        <View className='correct-word'>
                          <Text className='word-label'>正确单词:</Text>
                          <View className='word-text-container'>
                            <Text className='word-text'>{wrongWord.word.word}</Text>
                            <View
                              className='pronunciation-play-btn'
                              onClick={() => this.playWordPronunciation(wrongWord.word.word)}
                            >
                              <Text className='play-icon'>🔊</Text>
                            </View>
                          </View>
                        </View>

                        <View className='word-meaning'>
                          <Text className='meaning-label'>中文意思:</Text>
                          <Text className='meaning-text'>{wrongWord.word.meaning}</Text>
                        </View>

                        <View className='word-pronunciation'>
                          <Text className='pronunciation-label'>音标读音:</Text>
                          <Text className='pronunciation-text'>{wrongWord.word.pronunciation || '/'}</Text>
                        </View>

                        {wrongWord.userAnswer && (
                          <View className='user-answer'>
                            <Text className='answer-label'>你的回答:</Text>
                            <Text className='answer-text'>{wrongWord.userAnswer}</Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* 操作按钮区域 */}
          <View className='actions-section'>
            <Button className='primary-btn restart-btn' onClick={this.restartGame}>
              <Text className='btn-text'>再来一次</Text>
            </Button>

            <Button className='secondary-btn share-btn' onClick={this.generateShareImage}>
              <Text className='btn-text'>生成分享图</Text>
            </Button>

            <Button className='tertiary-btn home-btn' onClick={this.goHome}>
              <Text className='btn-text'>返回首页</Text>
            </Button>
          </View>

          {/* 推广区域 */}
          <View className='promotion-section' onClick={this.openKfChat}>
            <Text className='promotion-title'>🎯 想要更多练习？</Text>
            <Text className='promotion-subtitle'>加入我们的英语学习社群</Text>
          </View>

          {/* 分享时显示的小程序码区域 */}
          {showShareInfo && (
            <View className='share-qr-section'>
              <View className='share-info-left'>
                <Text className='share-tip-1'>长按识别小程序码</Text>
                <Text className='share-tip-2'>来发音挑战</Text>
              </View>
              <View className='share-qr-container'>
                <Image
                  className='share-qr-code'
                  src='https://gusto-english-oss.wemore.com/mp/guess_word.jpg'
                  mode='aspectFit'
                />
              </View>
            </View>
          )}
        </View>

        {/* 底部装饰 */}
        <View className='decoration-bottom'>
          <View className='wave wave-1'></View>
          <View className='wave wave-2'></View>
        </View>
      </View>
    )
  }
}
