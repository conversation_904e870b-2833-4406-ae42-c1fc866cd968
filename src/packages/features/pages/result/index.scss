/* 自定义导航栏模式下覆盖页面背景 */
page {
  background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 50%, #1E3A8A 100%) !important;
}

/* 结果页面样式 */
.result-container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 50%, #1E3A8A 100%);
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.decoration-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  pointer-events: none;
  z-index: 1;

  .star {
    position: absolute;
    font-size: 24px;
    animation: twinkle 2s infinite ease-in-out;

    &.star-1 {
      top: 30px;
      left: 20%;
      animation-delay: 0s;
    }

    &.star-2 {
      top: 80px;
      right: 25%;
      animation-delay: 0.7s;
    }

    &.star-3 {
      top: 50px;
      right: 10%;
      animation-delay: 1.4s;
    }
  }
}

/* 主内容包装器 */
.content-wrapper {
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px 0;
  width: 100%;
}

/* 庆祝区域 */
.celebration-section {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 60px;

  .title-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
  }

  .trophy-container {
    .trophy-emoji {
      font-size: 80px;
      display: block;
      animation: bounce 2s infinite;
    }
  }

  .congratulations-title {
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .performance-text {
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }
}

/* 成绩卡片 */
.score-section {
  margin-bottom: 30px;

  .score-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);

    .time-highlight {
      text-align: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 25px;

      .time-highlight-label {
        font-size: 18px;
        color: white;
        font-weight: 600;
        margin-bottom: 10px;
        display: block;
      }

      .time-highlight-value {
        font-size: 42px;
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        display: block;
      }
    }

    .score-main {
      text-align: center;
      margin-bottom: 25px;

      .score-label {
        font-size: 16px;
        color: #666;
        margin-bottom: 15px;
        display: block;
      }

      .score-display {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 15px;

        .score-number {
          font-size: 60px;
          font-weight: bold;
          color: #2196F3;
          line-height: 1;
        }

        .score-divider {
          font-size: 40px;
          color: #999;
          margin: 0 8px;
        }

        .score-total {
          font-size: 40px;
          color: #666;
          line-height: 1;
        }

        .score-unit {
          font-size: 30px;
          color: #666;
          margin-left: 8px;
          line-height: 1;
        }
      }

      .percentage-text {
        font-size: 24px;
        color: #4CAF50;
        font-weight: bold;
      }
    }

    .time-info {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #eee;

      .time-label {
        font-size: 18px;
        color: #666;
        margin-bottom: 8px;
        display: block;
      }

      .time-value {
        font-size: 24px;
        color: #FF9800;
        font-weight: bold;
      }
    }
  }
}

/* 错误单词回顾区域 */
.wrong-words-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .section-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    margin-bottom: 25px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  .wrong-words-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .wrong-word-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 18px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);

    .wrong-word-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;

      .wrong-word-number {
        font-size: 18px;
        font-weight: bold;
        color: #FF6B35;
      }

      .attempts-info {
        font-size: 14px;
        color: #666;
        background: #f5f5f5;
        padding: 4px 12px;
        border-radius: 12px;
      }
    }

    .wrong-word-content {
      display: flex;
      gap: 15px;

      .word-image-container {
        flex-shrink: 0;
        width: 100px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;

        .word-image {
          width: 100px;
          height: 100px;
          object-fit: cover;
        }

        .word-image-caption {
          margin-top: 6px;
          font-size: 12px;
          color: #555;
          text-align: center;
        }
      }

      .word-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .correct-word,
        .word-meaning,
        .word-pronunciation,
        .user-answer {
          display: flex;
          align-items: center;
          gap: 8px;

          .word-label,
          .meaning-label,
          .pronunciation-label,
          .answer-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            white-space: nowrap;
            min-width: 70px;
          }

          .word-text-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .word-text {
              font-size: 18px;
              font-weight: bold;
              color: #2196F3;
            }

            .pronunciation-play-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 48px;
              height: 48px;
              background: transparent;
              border-radius: 50%;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.1);
                background: rgba(0, 0, 0, 0.05);
              }

              &:active {
                transform: scale(0.95);
                background: rgba(0, 0, 0, 0.1);
              }

              .play-icon {
                font-size: 24px;
                color: #666;
              }
            }
          }

          .meaning-text {
            font-size: 16px;
            color: #333;
          }

          .pronunciation-text {
            font-size: 16px;
            color: #FF9800;
            font-family: 'Courier New', monospace;
          }

          .answer-text {
            font-size: 16px;
            color: #f44336;
            font-style: italic;
          }
        }
      }
    }
  }
}

/* 按钮区域 */
.actions-section {
  margin-bottom: 30px;

  .primary-btn,
  .secondary-btn,
  .tertiary-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    .btn-text {
      color: inherit;
      position: relative;
      font-size: 24px;
      z-index: 1;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:active::before {
      left: 100%;
    }
  }

  .primary-btn {
    background: linear-gradient(45deg, #FF6B35, #F29F67);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);

    &:active {
      transform: translateY(2px);
      box-shadow: 0 2px 10px rgba(255, 107, 53, 0.4);
    }
  }

  .secondary-btn {
    background: linear-gradient(45deg, #4CAF50, #81C784);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);

    &:active {
      transform: translateY(2px);
      box-shadow: 0 2px 10px rgba(76, 175, 80, 0.4);
    }
  }

  .tertiary-btn {
    background: linear-gradient(45deg, #9C27B0, #BA68C8);
    color: white;
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);

    &:active {
      transform: translateY(2px);
      box-shadow: 0 2px 10px rgba(156, 39, 176, 0.4);
    }
  }
}

/* 推广区域 */
.promotion-section {
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  //box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  .promotion-title {
    font-size: 18px;
    color: #f1e8e8;
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
  }

  .promotion-subtitle {
    font-size: 18px;
    color: #ebdbdb;
    margin-bottom: 20px;
    display: block;
  }

  .qr-container {
    .qr-code {
      width: 120px;
      height: 120px;
      border-radius: 10px;
      margin-bottom: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .qr-text {
      font-size: 16px;
      color: #999;
      display: block;
    }
  }
}

/* 分享时显示的小程序码区域 */
.share-qr-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);

  .share-info-left {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .share-tip-1 {
      font-size: 16px;
      color: #333;
      font-weight: bold;
    }

    .share-tip-2 {
      font-size: 14px;
      color: #666;
    }
  }

  .share-qr-container {
    .share-qr-code {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      border: 1px solid #eee;
    }
  }
}

/* 底部装饰 */
.decoration-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  pointer-events: none;
  z-index: 1;

  .wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;

    &.wave-1 {
      animation: wave 3s infinite ease-in-out;
    }

    &.wave-2 {
      height: 30px;
      background: rgba(255, 255, 255, 0.05);
      animation: wave 3s infinite ease-in-out reverse;
      animation-delay: 1s;
    }
  }
}

/* 动画定义 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-20px);
  }
}
