export default {
  pages: [
    'pages/index/index',
    'pages/game/index'
  ],
  subPackages: [
    {
      root: 'packages/user',
      pages: [
        'pages/history/index',
        'pages/ranking/index',
        'pages/share/index'
      ]
    },
    {
      root: 'packages/features',
      pages: [
        'pages/endless-mode/index',
        'pages/result/index'
      ]
    }
  ],
  window: {
  backgroundTextStyle: 'light',
  backgroundColor: '#ffffff',
  backgroundColorTop: '#ffffff',
  backgroundColorBottom: '#ffffff',
  navigationBarBackgroundColor: '#ffffff',
  navigationBarTitleText: '发音挑战',
  navigationBarTextStyle: 'black'
  },
  permission: {
    'scope.record': {
      desc: '您的录音权限将用于语音识别功能'
    },
    'scope.writePhotosAlbum': {
      desc: '您的相册权限将用于保存分享图片'
    }
  }
}
