import { FC } from 'react'
import { View, Text } from '@tarojs/components'
import '../../styles/_tokens.scss'

export type FeedbackType = 'success' | 'error' | 'info' | 'warn'

interface FeedbackProps {
  text: string
  type?: FeedbackType
}

export const Feedback: FC<FeedbackProps> = ({ text, type = 'info' }) => {
  if (!text) return null
  const cls = `feedback-block feedback--${type}`
  return (
    <View className={cls}>
      <Text className='feedback-text-inner'>{text}</Text>
    </View>
  )
}

export default Feedback
