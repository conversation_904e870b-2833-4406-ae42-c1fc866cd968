import { FC } from 'react'
import { View, Text } from '@tarojs/components'
import '../../styles/_tokens.scss'

interface StatGroupProps {
  progressCurrent: number
  progressTotal: number
  score: number
  attempts: number
  attemptsLimit?: number | '∞'
  elapsedSeconds: number
}

const formatTime = (s: number) => {
  const m = Math.floor(s / 60)
  const sec = s % 60
  return `${m.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`
}

export const StatGroup: FC<StatGroupProps> = ({ progressCurrent, progressTotal, score, attempts, attemptsLimit, elapsedSeconds }) => {
  return (
    <View className='stat-group'>
      <View className='stat-row'>
        <View className='stat-item'>
          <Text className='stat-label'>进度</Text>
          <Text className='stat-value'>{progressCurrent}/{progressTotal}</Text>
        </View>
        <View className='stat-item'>
          <Text className='stat-label'>用时</Text>
          <Text className='stat-value'>{formatTime(elapsedSeconds)}</Text>
        </View>
      </View>
      <View className='stat-row'>
        <View className='stat-item'>
          <Text className='stat-label'>得分</Text>
          <Text className='stat-value'>{score}</Text>
        </View>
        <View className='stat-item'>
          <Text className='stat-label'>尝试</Text>
          <Text className='stat-value'>{attempts}{attemptsLimit ? `/${attemptsLimit}` : ''}</Text>
        </View>
      </View>
    </View>
  )
}

export default StatGroup
