// 游戏相关类型定义
export interface Word {
  id: number
  word: string
  image: string
  tempImagePath?: string // 缓存的临时图片路径
  pronunciation: string // 音标
  meaning: string // 中文含义
  level?: number // 难度等级（1-5级，1最简单，5最困难）
}

export interface GameState {
  currentWordIndex: number
  score: number
  totalWords: number
  isRecording: boolean
  gameStatus: 'playing' | 'completed' | 'ready'
  attempts: number // 当前单词尝试次数
  startTime?: number // 游戏开始时间戳
  endTime?: number // 游戏结束时间戳
  lives?: number // 无尽模式生命值 (5颗心)
  correctStreak?: number // 连续答对次数
  currentLevel?: number // 当前难度等级 (1-5)
  currentBatchScore?: number // 当前批次答对的题目数
}

export interface ASRResponse {
  success: boolean
  text: string
  confidence: number
}

export interface WrongWordResult {
  word: Word
  userAnswer: string
  attempts: number
}

export interface GameHistory {
  id: string
  date: string
  score: number
  totalWords: number
  accuracy: number
  duration: number
  wrongWords: WrongWordResult[]
  gameMode: 'guess' | 'pronunciation' | 'endless' | 'endless-guess' | 'endless-pronunciation'
}

export interface ShareData {
  score: number
  totalWords: number
  accuracy: number
  duration: number
  wrongWords: WrongWordResult[]
  correctWords: Word[]
  date: string
}
