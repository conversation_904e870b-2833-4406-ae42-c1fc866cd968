// 用户相关类型定义
export interface UserInfo {
  openId: string
  phoneNumber?: string
  nickName?: string
  avatarUrl?: string
  isLoggedIn: boolean
}

export interface WeChatLoginResult {
  code: string
  userInfo?: any
}

export interface GameResultSubmit {
  openId: string
  score: number
  totalWords: number
  accuracy: number
  duration: number
  wrongWords: Array<{
    word: string
    userAnswer: string
    attempts: number
  }>
  completedAt: number
  gameMode: 'guess' | 'pronunciation' | 'endless' | 'endless_guess' | 'endless_challenge'
}