// 这个文件已废弃，请直接使用 wordService
// 为保持向后兼容性，保留此文件但建议直接从 services/wordService 导入

// 推荐的使用方式：
// import { wordService } from '../services/wordService'
// const gameWords = wordService.getAllWords()
// const randomWords = wordService.getRandomWords(5)

console.warn('警告：data/words.ts 已废弃，请直接使用 wordService')

export const gameWords: any[] = []
export const getRandomWords = (_count: number = 5): any[] => {
  console.error('请直接使用 wordService.getRandomWords() 而不是从 data/words 导入')
  return []
}
