/* 全局字体设置 - 使用无衬线字体 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
               'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
               sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 微信小程序组件字体继承 */
view, text, button, input, textarea, picker, radio, checkbox, switch, slider,
progress, navigator, image, icon, rich-text, map, canvas, video, camera,
live-player, live-pusher, web-view, ad, movable-view, movable-area,
cover-view, cover-image {
  font-family: inherit;
}

/* 让每个页面容器最少铺满视口，避免底部安全区露底色 */
.page-full-bleed,
page > view:first-child {
  min-height: 100vh;
  background: inherit;
}
