import { PropsWithChildren, Component } from 'react'
import { initSentry } from './utils/sentry'

import './app.scss'

interface AppProps extends PropsWithChildren<any> {}

class App extends Component<AppProps> {
  componentDidMount() {
    console.log('App launched.')
    // 初始化 Sentry 错误追踪
    initSentry()
  }

  render() {
    // children 是将要会渲染的页面
    return this.props.children
  }
}

export default App
