import * as Sentry from '@sentry/react'

import Taro from '@tarojs/taro'

// Sentry 配置
export const initSentry = () => {
  try {
    Sentry.init({
      dsn: 'https://<EMAIL>/50',
      environment: process.env.NODE_ENV || 'development',
      debug: process.env.NODE_ENV === 'development',
      // 性能监控采样率
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      // 错误采样率
      sampleRate: 1.0,
      // 在发送前过滤错误
      beforeSend(event, hint) {
        // 过滤掉一些不重要的错误
        if (event.exception) {
          const error = hint.originalException
          if (error && typeof error === 'object' && 'message' in error) {
            const message = error.message as string
            // 过滤网络错误和一些常见的小程序错误
            if (
              message.includes('Network Error') ||
              message.includes('request:fail') ||
              message.includes('getLocation:fail') ||
              message.includes('chooseImage:fail')
            ) {
              return null
            }
          }
        }
        return event
      },
      // 添加额外的上下文信息
      initialScope: {
        tags: {
          platform: 'miniprogram',
          framework: 'taro'
        },
        contexts: {
          device: {
            platform: Taro.getSystemInfoSync().platform,
            model: Taro.getSystemInfoSync().model,
            version: Taro.getSystemInfoSync().version
          }
        }
      }
    })

    // 设置用户信息（如果需要的话）
    Sentry.setContext('app', {
      name: 'word-pronunciation-game',
      version: '1.0.0'
    })

    console.log('Sentry 初始化成功')
  } catch (error) {
    console.error('Sentry 初始化失败:', error)
  }
}

// 手动捕获错误的工具函数
export const captureError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope(scope => {
    if (context) {
      Object.keys(context).forEach(key => {
        scope.setTag(key, context[key])
      })
    }
    Sentry.captureException(error)
  })
}

// 手动发送消息
export const captureMessage = (message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) => {
  Sentry.withScope(scope => {
    if (context) {
      Object.keys(context).forEach(key => {
        scope.setTag(key, context[key])
      })
    }
    Sentry.captureMessage(message, level)
  })
}

// 设置用户信息
export const setUser = (user: { id?: string; username?: string; email?: string }) => {
  Sentry.setUser(user)
}

// 添加面包屑
export const addBreadcrumb = (breadcrumb: { message: string; category?: string; level?: Sentry.SeverityLevel; data?: any }) => {
  Sentry.addBreadcrumb(breadcrumb)
}
