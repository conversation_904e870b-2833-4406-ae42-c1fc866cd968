import { getApiService } from './api'
import { userService } from './userService'

export type RankingType = 'guess' | 'pronunciation' | 'weekly' | 'total'

export interface RankingItem {
  id: string
  userName: string
  avatarUrl?: string
  score: number
  rank: number
  challengeCount?: number
  isCurrentUser?: boolean // 标记是否为当前用户
}

export interface RankingResponse {
  list: RankingItem[]
  userRank?: number
  userScore?: number
  totalCount?: number
}

class RankingService {
  // 获取排行榜数据
  async getRankingList(type: RankingType): Promise<RankingResponse> {
    try {
      // 优先尝试调用新的排行榜API
      const userInfo = userService.getUserInfo()
      const openId = userInfo?.openId

      try {
        const rankingData = await getApiService().getRanking(type, openId, {
          limit: 50 // 获取前50名
        })

        return {
          list: rankingData.list,
          userRank: rankingData.userRank,
          userScore: rankingData.userScore,
          totalCount: rankingData.totalCount
        }
      } catch (apiError) {
        console.warn('Failed to get ranking from server, falling back to game results processing:', apiError)

        // 如果排行榜API失败，回退到原来的逻辑（通过游戏结果计算）
        if (userService.isLoggedIn() && openId) {
          try {
            const gameResults = await getApiService().getUserGameResults(openId, {
              limit: 100, // 获取更多数据用于排行榜计算
              gameMode: this.getGameModeFromRankingType(type)
            })

            // 根据类型处理数据并生成排行榜
            return this.processGameResultsToRanking(gameResults, type)
          } catch (gameResultsError) {
            console.warn('Failed to get game results, using mock data:', gameResultsError)
          }
        }
      }

      // 如果所有API都失败，使用模拟数据
      return this.getMockRankingData(type)
    } catch (error) {
      console.error('Get ranking list failed:', error)
      throw new Error('获取排行榜失败')
    }
  }

  // 提交用户成绩
  async submitScore(type: RankingType, score: number): Promise<void> {
    try {
      // 实际应该调用服务器API
      // await api.post('/ranking/submit', { type, score })

      console.log('Submit score:', { type, score })
    } catch (error) {
      console.error('Submit score failed:', error)
      throw new Error('提交成绩失败')
    }
  }

  // 将排行榜类型转换为游戏模式
  private getGameModeFromRankingType(type: RankingType): string | undefined {
    switch (type) {
      case 'guess':
        return 'guess'
      case 'pronunciation':
        return 'pronunciation'
      case 'weekly':
      case 'total':
        return undefined // 不过滤游戏模式，获取所有数据
      default:
        return undefined
    }
  }

  // 处理游戏结果数据生成排行榜
  private processGameResultsToRanking(
    gameResults: {
      total: number
      results: Array<{
        resultId: string
        score: number
        totalWords: number
        accuracy: number
        duration: number
        gameMode: string
        completedAt: string
        wrongWordsCount: number
      }>
    },
    type: RankingType
  ): RankingResponse {
    const { results } = gameResults

    // 根据排行榜类型计算不同的统计数据
    let rankings: RankingItem[] = []

    switch (type) {
      case 'guess':
      case 'pronunciation':
        // 按最高分排序
        rankings = results
          .filter(r => r.gameMode === type)
          .sort((a, b) => b.score - a.score)
          .slice(0, 10)
          .map((result, index) => ({
            id: result.resultId,
            userName: `玩家${index + 1}`, // 实际应该从用户系统获取用户名
            score: result.score,
            rank: index + 1
          }))
        break

      case 'weekly':
        // 统计本周挑战次数（最近7天）
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)

        const weeklyResults = results.filter(r =>
          new Date(r.completedAt) >= weekAgo
        )

        rankings = [
          { id: '1', userName: '本周王者', score: weeklyResults.length, rank: 1 }
        ]
        break

      case 'total':
        // 统计总挑战次数
        rankings = [
          { id: '1', userName: '挑战达人', score: results.length, rank: 1 }
        ]
        break
    }

    // 如果没有真实数据，使用少量模拟数据
    if (rankings.length === 0) {
      rankings = this.getMockRankingData(type).list.slice(0, 3)
    }

    return {
      list: rankings,
      userRank: 1,
      userScore: rankings[0]?.score || 0
    }
  }

  // 模拟数据生成
  private getMockRankingData(type: RankingType): RankingResponse {
    const userInfo = userService.getUserInfo()
    const isLoggedIn = userService.isLoggedIn()
    
    // 生成前10名的模拟数据
    const mockUsers = [
      '发音达人', '语音王者', '单词专家', '英语高手', '挑战者',
      '学习狂人', '发音之星', '词汇大师', '语言天才', '练习达人'
    ]

    let baseScores: number[]
    let list: RankingItem[] = []

    switch (type) {
      case 'guess':
        baseScores = [950, 920, 890, 860, 830, 800, 770, 740, 710, 680]
        list = mockUsers.map((name, index) => ({
          id: `mock_${index + 1}`,
          userName: name,
          avatarUrl: '',
          score: Math.floor(baseScores[index] * 0.8), // 猜词模式分数稍低
          rank: index + 1,
          isCurrentUser: false
        }))
        break
      case 'pronunciation':
        baseScores = [950, 920, 890, 860, 830, 800, 770, 740, 710, 680]
        list = mockUsers.map((name, index) => ({
          id: `mock_${index + 1}`,
          userName: name,
          avatarUrl: '',
          score: baseScores[index],
          rank: index + 1,
          isCurrentUser: false
        }))
        break
      case 'weekly':
        baseScores = [45, 42, 38, 35, 32, 28, 25, 22, 18, 15]
        list = mockUsers.map((name, index) => ({
          id: `mock_${index + 1}`,
          userName: name,
          avatarUrl: '',
          score: baseScores[index],
          rank: index + 1,
          challengeCount: baseScores[index],
          isCurrentUser: false
        }))
        break
      case 'total':
        baseScores = [450, 420, 380, 350, 320, 280, 250, 220, 180, 150]
        list = mockUsers.map((name, index) => ({
          id: `mock_${index + 1}`,
          userName: name,
          avatarUrl: '',
          score: baseScores[index],
          rank: index + 1,
          challengeCount: baseScores[index],
          isCurrentUser: false
        }))
        break
    }

    // 如果用户已登录，在列表中添加或标注当前用户
    if (isLoggedIn && userInfo) {
      const userRank = 15
      const userScore = type === 'guess' ? 520 : type === 'pronunciation' ? 650 : type === 'weekly' ? 12 : 120
      
      // 检查当前用户是否已在前10名中
      const currentUserInTopTen = list.find(item => item.userName === userInfo.nickName)
      
      if (currentUserInTopTen) {
        // 如果在前10名中，标注为当前用户
        currentUserInTopTen.isCurrentUser = true
      } else {
        // 如果不在前10名中，添加到列表末尾显示用户排名
        list.push({
          id: userInfo.openId || 'current_user',
          userName: userInfo.nickName || '我',
          avatarUrl: userInfo.avatarUrl || '',
          score: userScore,
          rank: userRank,
          challengeCount: type === 'weekly' || type === 'total' ? userScore : undefined,
          isCurrentUser: true
        })
      }

      return {
        list,
        userRank,
        userScore,
        totalCount: 150
      }
    }

    return {
      list,
      userRank: undefined,
      userScore: undefined,
      totalCount: 150
    }
  }
}

export const rankingService = new RankingService()
