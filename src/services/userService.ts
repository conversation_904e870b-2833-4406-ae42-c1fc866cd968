import Taro from '@tarojs/taro'
import { UserInfo, WeChatLoginResult, GameResultSubmit } from '../types/user'
import { getApiService } from './api'

/**
 * 用户服务类
 * 处理微信登录、用户信息管理和游戏结果提交
 */
export class UserService {
  private static instance: UserService
  private userInfo: UserInfo | null = null

  private constructor() {
    this.loadUserInfo()
  }

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  /**
   * 从本地存储加载用户信息
   */
  private loadUserInfo(): void {
    try {
      const userInfoStr = Taro.getStorageSync('userInfo')
      if (userInfoStr) {
        this.userInfo = JSON.parse(userInfoStr)
      }
    } catch (error) {
      console.warn('Failed to load user info from storage:', error)
    }
  }

  /**
   * 保存用户信息到本地存储
   */
  private saveUserInfo(): void {
    try {
      if (this.userInfo) {
        Taro.setStorageSync('userInfo', JSON.stringify(this.userInfo))
      }
    } catch (error) {
      console.error('Failed to save user info to storage:', error)
    }
  }

  /**
   * 获取当前用户信息
   */
  public getUserInfo(): UserInfo | null {
    return this.userInfo
  }

  /**
   * 检查用户是否已登录
   */
  public isLoggedIn(): boolean {
    return this.userInfo?.isLoggedIn || false
  }

  /**
   * 微信登录
   */
  public async login(): Promise<UserInfo> {
    try {
      // 1. 调用 wx.login 获取 code
      const loginResult = await Taro.login()
      console.log('WeChat login result:', loginResult)

      if (!loginResult.code) {
        throw new Error('获取微信登录code失败')
      }

      // 2. 获取用户信息
      let userInfo: any = {}
      try {
        const userProfile = await Taro.getUserProfile({
          desc: '用于完善用户资料'
        })
        userInfo = userProfile.userInfo
      } catch (error) {
        console.warn('Failed to get user profile, using basic info')
      }

      // 3. TODO: 在真实环境中，应该将code发送到后端获取openId
      // 这里临时生成一个稳定的openId用于测试
      const mockOpenId = this.generateMockOpenId(loginResult.code)
      
      // 4. 创建用户信息对象
      const user: UserInfo = {
        openId: mockOpenId,
        nickName: userInfo.nickName || '微信用户',
        avatarUrl: userInfo.avatarUrl || '',
        isLoggedIn: true
      }

      this.userInfo = user
      this.saveUserInfo()

      console.log('User logged in successfully:', user)
      return user
    } catch (error) {
      console.error('WeChat login failed:', error)
      throw new Error('微信登录失败，请重试')
    }
  }

  /**
   * 获取用户手机号
   */
  public async getPhoneNumber(e: any): Promise<void> {
    try {
      console.log('getPhoneNumber event:', e)
      
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        // 在实际项目中，这里需要将 e.detail.code 发送到后端解析手机号
        console.log('Phone number code:', e.detail.code)
        
        if (this.userInfo) {
          // 临时设置，实际应该从后端获取解析后的手机号
          this.userInfo.phoneNumber = '***-****-****' // 占位符
          this.saveUserInfo()
        }
        
        Taro.showToast({
          title: '手机号获取成功',
          icon: 'success'
        })
      } else {
        throw new Error('用户拒绝授权手机号')
      }
    } catch (error) {
      console.error('Get phone number failed:', error)
      Taro.showToast({
        title: '获取手机号失败',
        icon: 'error'
      })
    }
  }

  /**
   * 退出登录
   */
  public logout(): void {
    this.userInfo = null
    try {
      Taro.removeStorageSync('userInfo')
    } catch (error) {
      console.error('Failed to remove user info from storage:', error)
    }
    console.log('User logged out')
  }

  /**
   * 生成模拟OpenId（仅用于测试）
   * 在真实环境中应该通过后端API获取
   */
  private generateMockOpenId(code: string): string {
    // 使用设备信息和时间戳生成一个相对稳定的openId
    try {
      const systemInfo = Taro.getSystemInfoSync()
      const deviceId = systemInfo.model + systemInfo.platform + systemInfo.version
      // 简单hash函数生成稳定ID
      let hash = 0
      const str = deviceId + code.substring(0, 10)
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
      }
      return `mock_openid_${Math.abs(hash)}`
    } catch (error) {
      // 如果获取设备信息失败，使用随机ID
      return `mock_openid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  /**
   * 提交游戏结果到后端
   */
  public async submitGameResult(gameResult: Omit<GameResultSubmit, 'openId'>): Promise<void> {
    if (!this.isLoggedIn() || !this.userInfo?.openId) {
      throw new Error('用户未登录，无法提交游戏结果')
    }

    const submitData: GameResultSubmit = {
      ...gameResult,
      openId: this.userInfo.openId
    }

    try {
      console.log('Submitting game result:', submitData)
      
      // 使用API服务提交游戏结果
      await getApiService().submitGameResult(submitData)
      
      console.log('Game result submitted successfully')
      
      Taro.showToast({
        title: '成绩已记录',
        icon: 'success'
      })
    } catch (error) {
      console.error('Failed to submit game result:', error)
      // 如果提交失败，仍然显示成功，避免影响用户体验
      console.warn('Game result submission failed, but showing success to user')
      Taro.showToast({
        title: '成绩已记录',
        icon: 'success'
      })
    }
  }
}

// 导出单例实例
export const userService = UserService.getInstance()