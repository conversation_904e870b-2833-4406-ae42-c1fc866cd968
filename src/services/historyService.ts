import Taro from '@tarojs/taro'
import { GameHistory, WrongWordResult } from '../types/game'

const HISTORY_STORAGE_KEY = 'game_history'

export class HistoryService {
  static async saveGameHistory(
    score: number,
    totalWords: number,
    duration: number,
    wrongWords: WrongWordResult[],
    gameMode: 'guess' | 'pronunciation' = 'guess'
  ): Promise<void> {
    try {
      const accuracy = Math.round((score / totalWords) * 100)
      const history: GameHistory = {
        id: Date.now().toString(),
        date: new Date().toISOString(),
        score,
        totalWords,
        accuracy,
        duration,
        wrongWords,
        gameMode
      }

      const existingHistory = await this.getAllHistory()
      const updatedHistory = [history, ...existingHistory]

      // 只保留最近50条记录
      const limitedHistory = updatedHistory.slice(0, 50)

      await Taro.setStorage({
        key: HISTORY_STORAGE_KEY,
        data: limitedHistory
      })

      console.log('游戏历史记录已保存:', history)
    } catch (error) {
      console.error('保存游戏历史记录失败:', error)
    }
  }

  static async getAllHistory(): Promise<GameHistory[]> {
    try {
      const result = await Taro.getStorage({
        key: HISTORY_STORAGE_KEY
      })
      return result.data || []
    } catch (error) {
      console.log('获取历史记录失败，返回空数组:', error)
      return []
    }
  }

  static async clearHistory(): Promise<void> {
    try {
      await Taro.removeStorage({
        key: HISTORY_STORAGE_KEY
      })
      console.log('历史记录已清空')
    } catch (error) {
      console.error('清空历史记录失败:', error)
    }
  }

  static formatDuration(duration: number): string {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  static formatDate(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const gameDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const diffTime = today.getTime() - gameDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return `今天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
    } else if (diffDays === 1) {
      return `昨天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}