import { Word } from '../types/game'

let wordsData: any = null

// 延迟加载数据，避免循环引用
function loadWordsData() {
  if (!wordsData) {
    try {
      wordsData = require('../data/words.json')
    } catch (error) {
      console.error('Failed to load words data:', error)
      wordsData = { words: [] }
    }
  }
  return wordsData
}

/**
 * 单词服务类
 * 负责管理单词数据的获取和处理
 */
export class WordService {
  private static instance: WordService | null = null
  private words: Word[] = []
  private initialized = false

  private constructor() {
    // 延迟初始化，避免构造函数中的循环引用
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): WordService {
    if (!WordService.instance) {
      WordService.instance = new WordService()
    }
    return WordService.instance
  }

  /**
   * 确保数据已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      this.loadLocalWords()
      this.initialized = true
    }
  }

  /**
   * 加载本地单词数据
   */
  private loadLocalWords(): void {
    try {
      const data = loadWordsData()
      this.words = (data?.words || []) as Word[]
      console.log(`Loaded ${this.words.length} words`)
    } catch (error) {
      console.error('Failed to load words data:', error)
      this.words = []
    }
  }

  /**
   * 从服务器获取单词数据
   * @param url 服务器API地址
   */
  public async loadWordsFromServer(url: string): Promise<Word[]> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()

      // 假设服务器返回的数据格式与本地JSON相同
      if (data && data.words && Array.isArray(data.words)) {
        this.words = data.words
        this.initialized = true
        return this.words
      } else {
        throw new Error('Invalid data format from server')
      }
    } catch (error) {
      console.error('Failed to load words from server:', error)
      // 如果服务器加载失败，使用本地数据
      this.loadLocalWords()
      return this.words
    }
  }

  /**
   * 获取所有单词
   */
  public getAllWords(): Word[] {
    this.ensureInitialized()
    return this.words.slice() // 使用 slice() 替代扩展运算符
  }

  /**
   * 获取随机单词组合
   * @param count 需要的单词数量
   */
  public getRandomWords(count: number = 5): Word[] {
    this.ensureInitialized()
    const shuffled = this.words.slice().sort(function() { return Math.random() - 0.5 })
    return shuffled.slice(0, Math.min(count, this.words.length))
  }

  /**
   * 根据ID获取单词
   * @param id 单词ID
   */
  public getWordById(id: number): Word | undefined {
    this.ensureInitialized()
    return this.words.find(word => word.id === id)
  }

  /**
   * 根据单词文本搜索
   * @param searchText 搜索文本
   */
  public searchWords(searchText: string): Word[] {
    this.ensureInitialized()
    const lowerSearchText = searchText.toLowerCase()
    return this.words.filter(function(word) {
      return word.word.toLowerCase().includes(lowerSearchText) ||
             word.meaning.includes(searchText)
    })
  }

  /**
   * 获取单词总数
   */
  public getWordCount(): number {
    this.ensureInitialized()
    return this.words.length
  }

  /**
   * 按难度级别获取单词（基于单词长度或难度字段）
   * @param level 难度级别：'easy' | 'medium' | 'hard' 或数字 1-5
   */
  public getWordsByDifficulty(level: 'easy' | 'medium' | 'hard' | number): Word[] {
    this.ensureInitialized()
    
    // 如果传入数字，直接按难度字段筛选
    if (typeof level === 'number') {
      return this.words.filter(function(word) { 
        return (word.difficulty || 1) === level 
      })
    }
    
    // 兼容原有的字符串级别，优先使用难度字段，回退到单词长度
    switch (level) {
      case 'easy':
        return this.words.filter(function(word) { 
          const difficulty = word.difficulty
          return difficulty ? difficulty <= 2 : word.word.length <= 4 
        })
      case 'medium':
        return this.words.filter(function(word) { 
          const difficulty = word.difficulty
          return difficulty ? difficulty === 3 : (word.word.length > 4 && word.word.length <= 7)
        })
      case 'hard':
        return this.words.filter(function(word) { 
          const difficulty = word.difficulty
          return difficulty ? difficulty >= 4 : word.word.length > 7
        })
      default:
        return this.words
    }
  }

  /**
   * 获取按难度排序的单词
   */
  public getWordsOrderedByDifficulty(): Word[] {
    this.ensureInitialized()
    return this.words.slice().sort(function(a, b) {
      const diffA = a.difficulty || 1
      const diffB = b.difficulty || 1
      return diffA - diffB
    })
  }
}

// 延迟初始化的获取方法，避免模块加载时的循环引用
export function getWordService(): WordService {
  return WordService.getInstance()
}
