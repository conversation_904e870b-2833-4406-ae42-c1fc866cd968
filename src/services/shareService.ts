import Taro from '@tarojs/taro'
import { ShareData } from '../types/game'

export class ShareService {
  static generateShareData(
    score: number,
    totalWords: number,
    duration: number,
    wrongWords: any[],
    correctWords: any[]
  ): ShareData {
    // 检查是否是无尽模式
    const isEndlessMode = totalWords === 0
    const accuracy = isEndlessMode ? 100 : Math.round((score / totalWords) * 100)
    const date = new Date().toISOString()
    
    return {
      score,
      totalWords,
      accuracy,
      duration,
      wrongWords,
      correctWords,
      date
    }
  }

  static formatDuration(duration: number): string {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  static async navigateToShare(shareData: ShareData): Promise<void> {
    try {
      const shareDataParam = encodeURIComponent(JSON.stringify(shareData))
      
      await Taro.navigateTo({
        url: `/packages/user/pages/share/index?data=${shareDataParam}`
      })
    } catch (error) {
      console.error('跳转到分享页面失败:', error)
      Taro.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  }

  static getPerformanceLevel(accuracy: number, score?: number, isEndless?: boolean): { level: string; emoji: string; color: string } {
    // 无尽模式根据答对数量评价
    if (isEndless && score !== undefined) {
      if (score >= 50) {
        return { level: '传奇', emoji: '👑', color: '#FFD700' }
      } else if (score >= 30) {
        return { level: '大师', emoji: '🏆', color: '#FF6B35' }
      } else if (score >= 15) {
        return { level: '优秀', emoji: '🎉', color: '#4CAF50' }
      } else if (score >= 5) {
        return { level: '良好', emoji: '👍', color: '#2196F3' }
      } else {
        return { level: '加油', emoji: '💪', color: '#9C27B0' }
      }
    }
    
    // 原有模式根据正确率评价
    if (accuracy >= 90) {
      return { level: '完美', emoji: '🏆', color: '#FFD700' }
    } else if (accuracy >= 80) {
      return { level: '优秀', emoji: '🌟', color: '#4CAF50' }
    } else if (accuracy >= 60) {
      return { level: '良好', emoji: '👍', color: '#2196F3' }
    } else if (accuracy >= 40) {
      return { level: '一般', emoji: '💪', color: '#FF9800' }
    } else {
      return { level: '加油', emoji: '🎯', color: '#f44336' }
    }
  }
}