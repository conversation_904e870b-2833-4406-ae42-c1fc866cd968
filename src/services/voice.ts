import Taro from '@tarojs/taro'
import { ASRResponse } from '../types/game'
import { captureError, addBreadcrumb } from '../utils/sentry'
import { API_CONFIG } from './api'

// 简化process类型声明，避免可能的循环引用
declare const process: any

class VoiceService {
  private recorderManager: any
  private isInitialized = false
  // 控制是否使用 Mock ASR，true 为使用 mock（全部通过），false 为使用真实 ASR
  private useMockASR = false
  // 避免重复展示系统级权限引导
  private permissionGuideShown = false

  constructor() {
    // 延迟初始化，避免在构造函数中执行可能引起循环的操作
    const self = this
    setTimeout(function() {
      self.initRecorder()
    }, 0)
  }

  // 引导用户前往系统设置开启微信麦克风权限
  private async showRecordPermissionGuide(): Promise<void> {
    try {
      if (this.permissionGuideShown) {
        console.log('权限引导已展示过，本次不再重复弹窗')
        return
      }
      const sys = Taro.getSystemInfoSync?.() as any
      const platform = sys?.platform || ''
      const isIOS = /ios/i.test(platform)
      const guide = isIOS
        ? '在 iPhone 设置 > 隐私与安全性 > 麦克风 中，打开“微信”的麦克风权限；然后重启微信并重新进入小程序。'
        : '在系统设置 > 应用管理 > 微信 > 权限 中，打开“麦克风”权限；然后返回微信并重新进入小程序。'

      await Taro.showModal({
        title: '开启麦克风权限',
        content: guide,
        showCancel: false,
        confirmText: '我知道了'
      })
      this.permissionGuideShown = true
    } catch (e) {
      console.warn('展示系统麦克风权限引导失败:', e)
    }
  }

  // 提供给页面调用的公开方法：展示录音权限开启引导
  async showRecordPermissionHelp(): Promise<void> {
    return this.showRecordPermissionGuide()
  }

  private initRecorder() {
    try {
      console.log('=== 录音管理器初始化开始 ===')
      console.log('Taro 可用:', typeof Taro !== 'undefined')
      console.log('getRecorderManager 方法可用:', typeof Taro !== 'undefined' && typeof Taro.getRecorderManager === 'function')

      // 获取系统信息进行环境检测
      if (typeof Taro !== 'undefined' && Taro.getSystemInfo) {
        Taro.getSystemInfo({
          success: (systemInfo) => {
            console.log('系统信息:', systemInfo)
            addBreadcrumb({
              message: '系统环境信息获取成功',
              category: 'system',
              level: 'info',
              data: {
                platform: systemInfo.platform,
                version: systemInfo.version,
                SDKVersion: systemInfo.SDKVersion
              }
            })
          },
          fail: (err) => {
            console.warn('获取系统信息失败:', err)
          }
        })
      }

      if (typeof Taro !== 'undefined' && Taro.getRecorderManager) {
        this.recorderManager = Taro.getRecorderManager()
        this.isInitialized = true
        console.log('录音管理器初始化成功')
        addBreadcrumb({
          message: '录音管理器初始化成功',
          category: 'voice',
          level: 'info'
        })
      } else {
        console.warn('录音管理器不可用')
        console.warn('Taro 对象:', typeof Taro)
        console.warn('getRecorderManager 方法:', typeof Taro !== 'undefined' ? typeof Taro.getRecorderManager : 'Taro不存在')

        addBreadcrumb({
          message: '录音管理器不可用',
          category: 'voice',
          level: 'warning',
          data: {
            taroAvailable: typeof Taro !== 'undefined',
            getRecorderManagerAvailable: typeof Taro !== 'undefined' && typeof Taro.getRecorderManager === 'function'
          }
        })
      }
      console.log('=== 录音管理器初始化完成 ===')
    } catch (error) {
      console.error('初始化录音管理器失败:', error)
      console.error('错误详情:', JSON.stringify(error, null, 2))
      captureError(error as Error, {
        context: 'initRecorder',
        errorDetails: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      })
    }
  }

  // 开始录音
  startRecord(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('=== 开始录音流程 ===')
      if (!this.isInitialized) {
        const error = new Error('录音功能未初始化')
        console.error('录音启动失败:', error)
        captureError(error, { context: 'startRecord_not_initialized' })
        reject(error)
        return
      }

      // 小程序录音配置
      const options = {
        duration: 10000, // 最长录音时间 10秒
        sampleRate: 16000, // 采样率
        numberOfChannels: 1, // 录音通道数
        encodeBitRate: 48000, // 编码码率
        format: 'mp3', // 音频格式
        frameSize: 50 // 指定帧大小，单位 KB
      }

      console.log('=== 录音开始调试信息 ===')
      console.log('录音配置:', options)
      console.log('录音管理器已初始化:', this.isInitialized)

      this.recorderManager.onStart(function() {
        console.log('录音成功启动')
        addBreadcrumb({
          message: '录音成功启动',
          category: 'recording',
          level: 'info'
        })
        resolve()
      })

  const self = this
  this.recorderManager.onError(function(err: any) {
        console.error('录音启动错误:', err)
        console.error('错误详情:', JSON.stringify(err, null, 2))

        // 捕获录音错误到 Sentry
        captureError(new Error(`录音启动失败: ${JSON.stringify(err)}`), {
          context: 'startRecord_recorder_error',
          errorDetails: err
        })

        addBreadcrumb({
          message: '录音启动失败',
          category: 'recording',
          level: 'error',
          data: err
        })

        // 可能的权限问题：尝试提示系统级权限开启
        try {
          const msg = (err && (err.errMsg || err.message || '')) as string
          // 缩小匹配范围，避免普通包含 record 字样的错误（如状态切换、停止事件）被误判
          const permissionRegex = /(auth|deny|denied|no\s+permission|permission|mic\b|microphone)/i
          if (msg) {
            console.log('录音错误消息内容:', msg)
            if (permissionRegex.test(msg)) {
              console.log('检测到可能的权限相关错误，准备展示权限引导')
              setTimeout(() => {
                self.showRecordPermissionGuide?.()
              }, 0)
            } else {
              console.log('错误不属于权限问题，跳过权限引导弹窗')
            }
          }
        } catch (_) { /* noop */ }

        reject(err)
      })

      try {
        this.recorderManager.start(options)
        console.log('调用录音管理器启动方法完成')
      } catch (startError) {
        console.error('调用录音启动方法异常:', startError)
        captureError(startError as Error, {
          context: 'startRecord_start_exception'
        })
        reject(startError)
      }
    })
  }

  // 停止录音并获取结果
  stopRecord(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.isInitialized) {
        reject(new Error('录音功能未初始化'))
        return
      }

      this.recorderManager.onStop(function(res: any) {
        console.log('录音结束:', res)
        resolve(res.tempFilePath)
      })

      this.recorderManager.stop()
    })
  }

  // 发送音频到后端进行ASR识别
  async sendToASR(audioPath: string, targetWord: string, gameMode?: 'guess' | 'pronunciation'): Promise<ASRResponse> {
    // 如果使用 Mock ASR，直接返回成功结果
    if (this.useMockASR) {
      return this.mockASRAlwaysPass(targetWord)
    }

    // 以下是真实的 ASR 实现
    try {
      // 动态获取 ASR 请求地址
      const asrUrl = `${API_CONFIG.BASE_URL}/asr`
      
      // 调试信息：打印请求参数
      console.log('=== ASR请求调试信息 ===')
      console.log('音频文件路径:', audioPath)
      console.log('目标单词:', targetWord)
      console.log('请求URL:', asrUrl)

      // 检查音频文件是否存在
      if (!audioPath) {
        throw new Error('音频文件路径为空')
      }

      // 检查文件信息
      try {
        const fileInfo = await Taro.getFileInfo({
          filePath: audioPath
        })
        console.log('音频文件信息:', fileInfo)
      } catch (fileError) {
        console.warn('无法获取文件信息:', fileError)
      }

      const formData = {
        target_word: targetWord
      }
      console.log('表单数据:', formData)

      // 使用实际的后端ASR接口
      const uploadResult = await Taro.uploadFile({
        url: asrUrl,
        filePath: audioPath,
        name: 'audio',
        formData: formData
      })

      // 调试信息：打印上传结果
      console.log('上传结果完整信息:', uploadResult)
      console.log('状态码:', uploadResult.statusCode)
      console.log('响应数据:', uploadResult.data)

      // 检查返回的数据
      if (!uploadResult || !uploadResult.data) {
        console.error('服务器返回空响应')
        throw new Error('Empty response from ASR server')
      }

      if (uploadResult.statusCode !== 200) {
        console.error('HTTP状态码错误:', uploadResult.statusCode)
        throw new Error(`HTTP错误: ${uploadResult.statusCode}`)
      }

      let response;
      try {
        response = JSON.parse(uploadResult.data)
        console.log('解析后的响应:', response)
      } catch (parseError) {
        console.error('Failed to parse ASR response:', parseError)
        console.error('原始响应数据:', uploadResult.data)
        throw new Error('Invalid response format from ASR server')
      }

      // 根据置信度判断是否成功
      const recognizedText = response.text || ''
      const confidence = response.confidence || 0
      // 发音挑战模式使用更高的置信度阈值
      const confidenceThreshold = gameMode === 'pronunciation' ? 0.8 : 0.7
      const isSuccess = confidence >= confidenceThreshold

      console.log('置信度判断:', {
        识别文本: recognizedText,
        置信度: confidence,
        游戏模式: gameMode,
        阈值: confidenceThreshold,
        是否成功: isSuccess
      })

      const result = {
        success: isSuccess, // 根据置信度判断是否成功
        text: recognizedText, // 保留原始识别文本
        confidence: confidence
      }
      console.log('最终返回结果:', result)
      console.log('=== ASR请求调试信息结束 ===')

      return result
    } catch (error) {
      console.error('ASR识别失败:', error)
      console.error('错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      })

      // 开发阶段的模拟响应
      if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
        console.log('使用模拟响应 (开发模式)')
        return this.mockASRResponse(targetWord)
      }

      // 返回一个默认的失败响应而不是抛出错误
      return {
        success: false,
        text: '',
        confidence: 0
      }
    }
  }

  // Mock ASR - 始终返回正确结果（用于调试）
  private async mockASRAlwaysPass(targetWord: string): Promise<ASRResponse> {
    console.log('=== Mock ASR 模式 - 始终通过 ===')
    console.log('目标单词:', targetWord)

    // 模拟一些处理时间
    await new Promise(resolve => setTimeout(resolve, 300))

    const result = {
      success: true,
      text: targetWord, // 始终返回正确的目标单词
      confidence: 0.95  // 高置信度
    }

    console.log('Mock ASR 结果:', result)
    console.log('=== Mock ASR 模式结束 ===')

    return result
  }

  // 开发阶段的模拟ASR响应
  private mockASRResponse(targetWord: string): ASRResponse {
    // 模拟50%的成功率
    const isCorrect = Math.random() > 0.5

    return {
      success: true,
      text: isCorrect ? targetWord : 'wrong_word',
      confidence: isCorrect ? 0.9 : 0.3
    }
  }

  // 切换 ASR 模式的方法
  setMockASR(useMock: boolean) {
    this.useMockASR = useMock
    console.log(`ASR 模式已切换为: ${useMock ? 'Mock模式（始终通过）' : '真实ASR模式'}`)
  }

  // 获取当前 ASR 模式
  getMockASRStatus(): boolean {
    return this.useMockASR
  }

  // 检查录音权限
  async checkRecordPermission(): Promise<boolean> {
    try {
      console.log('=== 开始检查录音权限 ===')
      const { authSetting } = await Taro.getSetting()
      console.log('获取到的权限设置:', JSON.stringify(authSetting, null, 2))
      console.log('录音权限具体值:', authSetting['scope.record'])

      addBreadcrumb({
        message: '检查录音权限',
        category: 'permission',
        level: 'info',
        data: {
          authSetting: authSetting,
          recordPermission: authSetting['scope.record']
        }
      })

      // 情况1：用户明确拒绝过
      if (authSetting['scope.record'] === false) {
        console.log('权限被明确拒绝，引导用户去设置')
        const { confirm } = await Taro.showModal({
          title: '需要录音权限',
          content: '语音识别功能需要录音权限，请在设置中开启',
          confirmText: '去设置',
          cancelText: '取消'
        })

        if (confirm) {
          console.log('用户同意去设置页面')
          await Taro.openSetting()
          // 打开设置后再次检查
          const settingAfter = await Taro.getSetting()
          console.log('从设置页回来后的权限:', settingAfter.authSetting['scope.record'])
          if (!!settingAfter.authSetting['scope.record']) return true
          // 设置页无开关或仍未开启，给出系统级引导
          console.log('设置页仍未开启，显示系统级引导')
          await this.showRecordPermissionGuide()
          return false
        }
        console.log('用户拒绝去设置页面')
        return false
      }

      // 情况2：首次进入（未出现过授权框），此时 authSetting['scope.record'] 可能为 undefined
      if (!authSetting['scope.record']) {
        console.log('权限未设置，尝试申请授权')
        try {
          await Taro.authorize({ scope: 'scope.record' })
          console.log('授权申请成功')
          return true
    } catch (e) {
          console.log('授权申请被拒绝:', e)
          // 调用 authorize 被拒绝，提示去设置
          const { confirm } = await Taro.showModal({
            title: '需要录音权限',
            content: '您已拒绝录音权限，是否前往设置开启？',
            confirmText: '去设置',
            cancelText: '取消'
          })
          if (confirm) {
            console.log('用户同意去设置页面(授权被拒后)')
            await Taro.openSetting()
            const settingAfter = await Taro.getSetting()
            console.log('从设置页回来后的权限(授权被拒后):', settingAfter.authSetting['scope.record'])
      if (!!settingAfter.authSetting['scope.record']) return true
      await this.showRecordPermissionGuide()
      return false
          }
          console.log('用户拒绝去设置页面(授权被拒后)')
          return false
        }
      }

      // 已授权
      console.log('权限已授权，直接返回true')
      console.log('=== 权限检查完成 ===')
      return true
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }
}

export const voiceService = new VoiceService()
