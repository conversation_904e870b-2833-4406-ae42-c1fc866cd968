import Taro from '@tarojs/taro'
import { addBreadcrumb, captureError } from '../utils/sentry'

// 导入音频文件
import correctSound from '../sound/Correct_Sound.mp3'
import errorSound from '../sound/Error_Sound.mp3'
import completionSound from '../sound/Celebrate_Completion.mp3'

type SoundType = 'correct' | 'error' | 'completion'

class AudioService {
  private audioContext: any = null
  private isInitialized = false
  
  // 音频文件路径映射
  private soundPaths: Record<SoundType, string> = {
    correct: correctSound,
    error: errorSound, 
    completion: completionSound
  }

  constructor() {
    this.init()
  }

  private init() {
    try {
      // 初始化音频上下文
      if (typeof Taro !== 'undefined' && Taro.createInnerAudioContext) {
        this.isInitialized = true
        addBreadcrumb({
          message: '音频服务初始化成功',
          category: 'audio',
          level: 'info'
        })
      } else {
        console.warn('音频功能不可用')
        addBreadcrumb({
          message: '音频功能不可用',
          category: 'audio', 
          level: 'warning'
        })
      }
    } catch (error) {
      console.error('音频服务初始化失败:', error)
      captureError(error as Error, {
        context: 'AudioService.init'
      })
    }
  }

  /**
   * 播放指定类型的音效
   * @param soundType 音效类型
   */
  async playSound(soundType: SoundType): Promise<void> {
    if (!this.isInitialized) {
      console.warn('音频服务未初始化，跳过播放')
      return
    }

    try {
      const audioContext = Taro.createInnerAudioContext()
      audioContext.src = this.soundPaths[soundType]
      audioContext.volume = 0.5 // 设置音量为50%
      
      return new Promise((resolve, reject) => {
        audioContext.onPlay(() => {
          addBreadcrumb({
            message: `开始播放音效: ${soundType}`,
            category: 'audio',
            level: 'info'
          })
        })

        audioContext.onEnded(() => {
          addBreadcrumb({
            message: `音效播放完成: ${soundType}`,
            category: 'audio',
            level: 'info'
          })
          audioContext.destroy()
          resolve()
        })

        audioContext.onError((error) => {
          console.error(`音效播放失败: ${soundType}`, error)
          captureError(new Error(`音效播放失败: ${soundType}`), {
            context: 'AudioService.playSound',
            soundType,
            error
          })
          audioContext.destroy()
          reject(error)
        })

        audioContext.play()
      })
    } catch (error) {
      console.error(`播放音效失败: ${soundType}`, error)
      captureError(error as Error, {
        context: 'AudioService.playSound',
        soundType
      })
    }
  }

  /**
   * 播放答对音效
   */
  async playCorrectSound(): Promise<void> {
    return this.playSound('correct')
  }

  /**
   * 播放答错音效
   */
  async playErrorSound(): Promise<void> {
    return this.playSound('error')
  }

  /**
   * 播放完成庆祝音效
   */
  async playCompletionSound(): Promise<void> {
    return this.playSound('completion')
  }

  /**
   * 播放单词读音
   * @param word 单词
   */
  async playWordPronunciation(word: string): Promise<void> {
    if (!this.isInitialized) {
      console.warn('音频服务未初始化，跳过播放')
      return
    }

    try {
      const audioContext = Taro.createInnerAudioContext()
      const audioUrl = `https://gusto-english-oss.wemore.com/mp/audio/${word.toLowerCase()}.mp3`
      audioContext.src = audioUrl
      audioContext.volume = 0.8 // 设置音量为80%
      
      return new Promise((resolve, reject) => {
        audioContext.onPlay(() => {
          addBreadcrumb({
            message: `开始播放单词读音: ${word}`,
            category: 'audio',
            level: 'info'
          })
        })

        audioContext.onEnded(() => {
          addBreadcrumb({
            message: `单词读音播放完成: ${word}`,
            category: 'audio',
            level: 'info'
          })
          audioContext.destroy()
          resolve()
        })

        audioContext.onError((error) => {
          console.error(`单词读音播放失败: ${word}`, error)
          captureError(new Error(`单词读音播放失败: ${word}`), {
            context: 'AudioService.playWordPronunciation',
            word,
            audioUrl,
            error
          })
          audioContext.destroy()
          reject(error)
        })

        audioContext.play()
      })
    } catch (error) {
      console.error(`播放单词读音失败: ${word}`, error)
      captureError(error as Error, {
        context: 'AudioService.playWordPronunciation',
        word
      })
    }
  }

  /**
   * 检查音频服务是否可用
   */
  isAvailable(): boolean {
    return this.isInitialized
  }
}

export const audioService = new AudioService()