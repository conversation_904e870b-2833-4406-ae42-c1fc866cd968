import Taro from '@tarojs/taro'

/**
 * 客服服务
 */
export class CustomerService {
  /**
   * 打开企业微信「微信客服」会话
   */
  static openKfChat = () => {
    try {
      const kfUrl = 'https://work.weixin.qq.com/kfid/kfc48bfe22966dceaf1'
      const corpId = 'ww5901fd1f60a090f4'

      // 优先使用小程序原生能力
      const anyTaro: any = Taro as any
      if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP && typeof anyTaro.openCustomerServiceChat === 'function') {
        anyTaro.openCustomerServiceChat({
          extInfo: { url: kfUrl },
          corpId,
          success: () => {},
          fail: (err) => {
            console.error('openCustomerServiceChat fail:', err)
            // 兜底：复制链接
            Taro.setClipboardData({ data: kfUrl })
            Taro.showToast({ title: '已复制客服链接，请在微信中打开', icon: 'none' })
          }
        })
        return
      }

      // H5 或其他端：直接跳转/复制
      if (process.env.TARO_ENV === 'h5' && typeof window !== 'undefined') {
        window.open(kfUrl, '_blank')
        return
      }

      // 其他端兜底：复制链接
      Taro.setClipboardData({ data: kfUrl })
      Taro.showToast({ title: '已复制客服链接，请在微信中打开', icon: 'none' })
    } catch (e) {
      console.error('openKfChat error:', e)
      Taro.showToast({ title: '打开客服失败', icon: 'none' })
    }
  }
}