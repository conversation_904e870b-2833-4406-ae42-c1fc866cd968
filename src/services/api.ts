import Taro from '@tarojs/taro'
import { WordService } from './wordService'
import { Word } from '../types/game'
import { GameResultSubmit } from '../types/user'

/**
 * 获取当前小程序环境
 */
function getAppEnvironment(): 'trial' | 'release' | 'develop' {
  try {
    const accountInfo = Taro.getAccountInfoSync()
    return accountInfo.miniProgram.envVersion || 'release'
  } catch (error) {
    console.warn('Failed to get app environment, defaulting to release:', error)
    return 'release'
  }
}

/**
 * 根据环境获取 API 基础 URL
 */
function getApiBaseUrl(): string {
  const env = getAppEnvironment()

  switch (env) {
    case 'trial':
      // 体验版使用测试环境
      return 'https://apitest.wemore.com/word-guess'
    case 'release':
      // 正式版使用正式环境
      return 'https://api.wemore.com/word-guess'
    case 'develop':
      // 开发版使用测试环境
      return 'https://apitest.wemore.com/word-guess'
    default:
      return 'https://api.wemore.com/word-guess'
  }
}

/**
 * API 配置
 */
export const API_CONFIG = {
  // 根据环境动态设置服务器地址
  BASE_URL: getApiBaseUrl(),
  ENDPOINTS: {
    WORDS: '/words',
    WORD_BY_ID: '/words/:id',
    RANDOM_WORDS: '/api/v1/words/random',
    PRONUNCIATION_CHALLENGE: '/api/v1/words/pronunce_challenge',
    SEARCH_WORDS: '/words/search',
    ORDERED_BY_DIFFICULTY: '/api/v1/words/ordered-by-difficulty',
    RANDOM_ORDERED_BY_DIFFICULTY: '/api/v1/words/random-ordered-by-difficulty',
    PRONUNCIATION_ORDERED_BY_DIFFICULTY: '/api/v1/words/pronunciation-ordered-by-difficulty',
    SUBMIT_GAME_RESULT: '/api/v1/game-results',
    GET_USER_GAME_RESULTS: '/api/v1/game-results',
    GET_RANKING: '/api/v1/ranking'
  }
}

/**
 * 服务器响应格式
 */
export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
}

/**
 * API 服务类
 * 处理与服务器的通信
 */
export class ApiService {
  private static instance: ApiService
  private baseUrl: string

  private constructor() {
    this.baseUrl = API_CONFIG.BASE_URL
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService()
    }
    return ApiService.instance
  }

  /**
   * 设置 API 基础 URL
   * @param url 基础 URL
   */
  public setBaseUrl(url: string): void {
    this.baseUrl = url
  }

  /**
   * 通用请求方法
   * @param endpoint 端点
   * @param data 请求参数
   * @param method HTTP方法
   */
  private async request<T>(endpoint: string, data?: any, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET'): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    try {
      console.log(`API请求: ${url}`, data)

      const response = await Taro.request({
        url,
        method,
        data,
        header: {
          'Content-Type': 'application/json'
        }
      })

      console.log(`API响应: ${url}`, response)

      if (response.statusCode !== 200) {
        throw new Error(`HTTP error! status: ${response.statusCode}`)
      }

      // 检查服务器响应格式
      const responseData = response.data as any

      // 检查是否有错误信息
      if (responseData.errMsg && responseData.errMsg !== 'request:ok') {
        throw new Error(responseData.errMsg || 'API请求失败')
      }

      // 如果响应数据符合 ApiResponse 格式
      if (responseData.success !== undefined) {
        const apiResponse = responseData as ApiResponse<T>
        if (!apiResponse.success) {
          throw new Error(apiResponse.message || 'API请求失败')
        }
        return apiResponse.data
      }

      // 否则直接返回响应数据
      return responseData as T
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error)
      throw error
    }
  }

  /**
   * 获取游戏单词（随机获取指定数量）
   * @param count 数量，默认5个
   * @param level 难度级别
   */
  public async fetchGameWords(count: number = 5, level?: 'easy' | 'medium' | 'hard'): Promise<Word[]> {
    try {
      const params: any = { count }
      if (level) {
        params.level = level
      }

      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.RANDOM_WORDS, params)
      console.log('服务端返回的游戏单词数据 (fetchGameWords):', JSON.stringify(data.words.slice(0, 2), null, 2))
      return data.words
    } catch (error) {
      console.warn('Failed to fetch game words from server, using local data')
      return WordService.getInstance().getRandomWords(count)
    }
  }

  /**
   * 获取按难度排序的所有单词
   */
  public async fetchOrderedByDifficulty(): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.ORDERED_BY_DIFFICULTY)
      console.log('服务端返回的按难度排序单词数据:', JSON.stringify(data.words.slice(0, 3), null, 2))
      return data.words
    } catch (error) {
      console.warn('Failed to fetch ordered words from server, using local data')
      return WordService.getInstance().getAllWords()
    }
  }

  /**
   * 获取保持难度顺序的随机单词
   * @param level 难度等级，1-5级，可选，不传则返回混合难度
   * @param count 数量，默认5个
   */
  public async fetchRandomOrderedByDifficulty(level?: number, count: number = 5): Promise<Word[]> {
    try {
      const params: any = { count }
      if (level !== undefined) {
        params.level = level
      }
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.RANDOM_ORDERED_BY_DIFFICULTY, params)
      console.log(`服务端返回的看图猜词${level ? `Level${level}` : '混合难度'}单词数据:`, JSON.stringify(data.words.slice(0, 3), null, 2))
      return data.words
    } catch (error) {
      console.warn('Failed to fetch random ordered words from server, using local data')
      return WordService.getInstance().getRandomWords(count)
    }
  }

  /**
   * 获取发音挑战专用的按难度排序单词
   * @param level 难度等级，1-5级
   * @param count 数量，默认5个
   * @param forceRefresh 强制刷新，避免缓存，默认false
   */
  public async fetchPronunciationOrderedByDifficulty(level: number = 1, count: number = 5, forceRefresh: boolean = false): Promise<Word[]> {
    try {
      const params: any = { level, count }
      // 添加时间戳参数强制刷新
      if (forceRefresh) {
        params._t = Date.now()
      }
      console.log(`🌐 API调用: fetchPronunciationOrderedByDifficulty 参数:`, { level, count, forceRefresh, params })
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.PRONUNCIATION_ORDERED_BY_DIFFICULTY, params)
      console.log(`📦 服务端返回的发音挑战Level${level}单词数据${forceRefresh ? '(强制刷新)' : ''}:`, JSON.stringify(data.words.slice(0, 3).map(w => ({ word: w.word, level: w.level })), null, 2))
      return data.words
    } catch (error) {
      console.warn('Failed to fetch pronunciation ordered words from server, using local data')
      // 如果发音挑战专用API失败，回退到通用的发音挑战API
      return this.fetchPronunciationChallengeWords(count)
    }
  }

  /**
   * 获取发音挑战单词
   * @param count 数量，默认5个
   */
  public async fetchPronunciationChallengeWords(count: number = 5): Promise<Word[]> {
    try {
      const params = { count }
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.PRONUNCIATION_CHALLENGE, params)
      return data.words
    } catch (error) {
      console.warn('Failed to fetch pronunciation challenge words from server, using local data')
      return WordService.getInstance().getRandomWords(count)
    }
  }

  /**
   * 获取所有单词
   */
  public async fetchWords(): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(API_CONFIG.ENDPOINTS.WORDS)
      return data.words
    } catch (error) {
      console.warn('Failed to fetch words from server, using local data')
      return WordService.getInstance().getAllWords()
    }
  }

  /**
   * 获取随机单词
   * @param count 数量
   */
  public async fetchRandomWords(count: number = 5): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(
        API_CONFIG.ENDPOINTS.RANDOM_WORDS,
        { count }
      )
      return data.words
    } catch (error) {
      console.warn('Failed to fetch random words from server, using local data')
      return WordService.getInstance().getRandomWords(count)
    }
  }

  /**
   * 根据ID获取单词
   * @param id 单词ID
   */
  public async fetchWordById(id: number): Promise<Word | null> {
    try {
      const endpoint = API_CONFIG.ENDPOINTS.WORD_BY_ID.replace(':id', id.toString())
      const data = await this.request<{ word: Word }>(endpoint)
      return data.word
    } catch (error) {
      console.warn(`Failed to fetch word ${id} from server, using local data`)
      return WordService.getInstance().getWordById(id) || null
    }
  }

  /**
   * 搜索单词
   * @param query 搜索关键词
   */
  public async searchWords(query: string): Promise<Word[]> {
    try {
      const data = await this.request<{ words: Word[] }>(
        API_CONFIG.ENDPOINTS.SEARCH_WORDS,
        { q: query }
      )
      return data.words
    } catch (error) {
      console.warn('Failed to search words from server, using local data')
      return WordService.getInstance().searchWords(query)
    }
  }

  /**
   * 提交游戏结果
   * @param gameResult 游戏结果数据
   */
  public async submitGameResult(gameResult: GameResultSubmit): Promise<boolean> {
    try {
      console.log('Submitting game result to server:', gameResult)

      const response = await this.request<{ success: boolean }>(
        API_CONFIG.ENDPOINTS.SUBMIT_GAME_RESULT,
        gameResult,
        'POST'
      )

      console.log('Game result submitted successfully:', response)
      return response.success || true
    } catch (error) {
      console.error('Failed to submit game result to server:', error)
      throw error
    }
  }

  /**
   * 获取用户游戏历史
   * @param openId 用户openId
   * @param options 查询选项
   */
  public async getUserGameResults(
    openId: string,
    options?: {
      limit?: number
      offset?: number
      gameMode?: string
    }
  ): Promise<{
    total: number
    results: Array<{
      resultId: string
      score: number
      totalWords: number
      accuracy: number
      duration: number
      gameMode: string
      completedAt: string
      wrongWordsCount: number
    }>
  }> {
    try {
      const endpoint = API_CONFIG.ENDPOINTS.GET_USER_GAME_RESULTS
      const params: any = { openId }

      if (options?.limit) params.limit = options.limit
      if (options?.offset) params.offset = options.offset
      if (options?.gameMode) params.gameMode = options.gameMode

      const data = await this.request<{
        total: number
        results: Array<{
          resultId: string
          score: number
          totalWords: number
          accuracy: number
          duration: number
          gameMode: string
          completedAt: string
          wrongWordsCount: number
        }>
      }>(endpoint, params)

      return data
    } catch (error) {
      console.error('Failed to get user game results:', error)
      throw error
    }
  }

  /**
   * 获取排行榜数据
   * @param type 排行榜类型
   * @param openId 当前用户openId，用于标注用户在排行榜中的位置
   * @param options 查询选项
   */
  public async getRanking(
    type: 'guess' | 'pronunciation' | 'weekly' | 'total',
    openId?: string,
    options?: {
      limit?: number
      offset?: number
    }
  ): Promise<{
    list: Array<{
      id: string
      userName: string
      avatarUrl?: string
      score: number
      rank: number
      challengeCount?: number
      isCurrentUser?: boolean // 标记是否为当前用户
    }>
    userRank?: number
    userScore?: number
    totalCount: number
  }> {
    try {
      const params: any = { type }
      
      if (openId) params.openId = openId
      if (options?.limit) params.limit = options.limit
      if (options?.offset) params.offset = options.offset

      const data = await this.request<{
        list: Array<{
          id: string
          userName: string
          avatarUrl?: string
          score: number
          rank: number
          challengeCount?: number
          isCurrentUser?: boolean
        }>
        userRank?: number
        userScore?: number
        totalCount: number
      }>(API_CONFIG.ENDPOINTS.GET_RANKING, params)

      return data
    } catch (error) {
      console.error('Failed to get ranking data:', error)
      throw error
    }
  }

  /**
   * 初始化数据
   * 尝试从服务器加载数据，失败则使用本地数据
   */
  public async initializeData(): Promise<void> {
    try {
      const words = await this.fetchGameWords(5)
      console.log(`Successfully loaded ${words.length} words from server`)
    } catch (error) {
      console.log('Using local word data')
    }
  }
}

// 延迟初始化的获取方法，避免模块加载时的循环引用
export function getApiService(): ApiService {
  return ApiService.getInstance()
}

/**
 * 使用示例：
 *
 * // 在应用启动时初始化数据
 * await getApiService().initializeData()
 *
 * // 获取随机单词
 * const randomWords = await getApiService().fetchRandomWords(10)
 *
 * // 搜索单词
 * const searchResults = await getApiService().searchWords('apple')
 *
 * // 设置自定义服务器地址
 * getApiService().setBaseUrl('https://my-custom-server.com/api')
 */
