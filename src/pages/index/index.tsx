import { View, Text, Image, Button } from '@tarojs/components'
import { Component } from 'react'
import Taro from '@tarojs/taro'
import { userService } from '../../services/userService'
import { CustomerService } from '../../services/customerService'
import type { UserInfo } from '../../types/user'
import './index.scss'

type State = {
  userInfo: UserInfo | null
  showUserMenu: boolean
}

export default class Index extends Component<{}, State> {
  constructor(props) {
    super(props)
    this.state = {
      userInfo: null,
      showUserMenu: false
    }
    this.startGame = this.startGame.bind(this)
    this.startPronunciationChallenge = this.startPronunciationChallenge.bind(this)
    this.startEndlessMode = this.startEndlessMode.bind(this)
    this.goToHistory = this.goToHistory.bind(this)
  }

  componentDidMount() {
    console.log('Page loaded.')
    // 启用分享功能
    Taro.showShareMenu({
      withShareTicket: true
    })
    // 加载用户信息
    this.loadUserInfo()
  }

  loadUserInfo = () => {
    const userInfo = userService.getUserInfo()
    this.setState({ userInfo })
  }

  // 配置分享功能
  onShareAppMessage() {
    return {
      title: '来挑战单词发音吧',
      path: '/pages/index/index'
      // 不设置 imageUrl，让微信自动截取页面内容作为分享图片
    }
  }

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '来挑战单词发音吧'
      // 不设置 imageUrl，让微信自动截取页面内容作为分享图片
    }
  }

  startGame() {
    Taro.navigateTo({
      url: '/pages/game/index?mode=guess'
    })
  }

  startPronunciationChallenge() {
    Taro.navigateTo({
      url: '/pages/game/index?mode=pronunciation'
    })
  }

  startEndlessMode() {
    Taro.navigateTo({
      url: '/packages/features/pages/endless-mode/index'
    })
  }

  goToHistory() {
    Taro.navigateTo({
      url: '/packages/user/pages/history/index'
    })
  }

  // 跳转到排行榜页面
  handleRankingClick = () => {
    Taro.navigateTo({
      url: '/packages/user/pages/ranking/index'
    })
  }

  // 移除二维码弹窗逻辑，卡片改为直接打开客服

  handleAvatarClick = () => {
    if (userService.isLoggedIn()) {
      this.setState({ showUserMenu: true })
    } else {
      this.handleLogin()
    }
  }

  handleLogin = async () => {
    try {
      Taro.showLoading({ title: '登录中...' })
      const userInfo = await userService.login()
      this.setState({ userInfo })
      Taro.hideLoading()
      Taro.showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error) {
      Taro.hideLoading()
      Taro.showToast({
        title: error.message || '登录失败',
        icon: 'error'
      })
    }
  }

  handleLogout = () => {
    userService.logout()
    this.setState({ userInfo: null, showUserMenu: false })
    Taro.showToast({
      title: '已退出登录',
      icon: 'success'
    })
  }

  handleGetPhoneNumber = async (e) => {
    try {
      await userService.getPhoneNumber(e)
      this.loadUserInfo()
    } catch (error) {
      console.error('Get phone number failed:', error)
    }
  }

  closeUserMenu = () => {
    this.setState({ showUserMenu: false })
  }

  render() {
    return (
      <View className='index'>
        <Image
          className='bg-image'
          src={require('../../assets/bg.png')}
          mode='aspectFill'
        />
        <View className='home'>
          {/* 品牌标题 */}
          <View className='brand-block'>
            <Text className='brand-en'>Gusto English</Text>
            <Text className='brand-zh'>口语发音挑战</Text>
          </View>

          {/* 2x2 功能网格 */}
          <View className='menu-grid'>
            <View className='menu-card orange' onClick={this.startPronunciationChallenge}>
              <Text className='menu-icon'>👤</Text>
              <Text className='menu-title'>发音挑战</Text>
            </View>

            <View className='menu-card purple' onClick={this.startEndlessMode}>
              <Text className='menu-icon'>🎮</Text>
              <Text className='menu-title'>无尽模式</Text>
            </View>

            <View className='menu-card green' onClick={this.startGame}>
              <Text className='menu-icon'>🖼️</Text>
              <Text className='menu-title'>看图猜词</Text>
            </View>

            <View className='menu-card yellow' onClick={this.handleRankingClick}>
              <Text className='menu-icon'>📊</Text>
              <Text className='menu-title'>排行榜</Text>
            </View>
          </View>

          {/* 福利群横幅 */}
          <View className='footer-banner' onClick={CustomerService.openKfChat}>
            <Text>✨ 加入免费口语福利群 ✨</Text>
          </View>

          {/* 用户菜单 Modal（保留） */}
          {this.state.showUserMenu && (
            <View className='user-menu-overlay' onClick={this.closeUserMenu}>
              <View className='user-menu' onClick={(e) => e.stopPropagation()}>
                <View className='user-menu-content'>
                  <View className='user-info'>
                    <Image
                      className='menu-avatar'
                      src={this.state.userInfo?.avatarUrl || 'https://gusto-english-oss.wemore.com/default-avatar.png'}
                      mode='aspectFill'
                    />
                    <Text className='user-name'>{this.state.userInfo?.nickName || '微信用户'}</Text>
                    {this.state.userInfo?.phoneNumber && (
                      <Text className='user-phone'>{this.state.userInfo.phoneNumber}</Text>
                    )}
                  </View>

                  {!this.state.userInfo?.phoneNumber && (
                    <Button
                      className='phone-btn'
                      openType='getPhoneNumber'
                      onGetPhoneNumber={this.handleGetPhoneNumber}
                    >
                      获取手机号
                    </Button>
                  )}

                  <Button className='logout-btn' onClick={this.handleLogout}>
                    退出登录
                  </Button>

                  <Button className='close-menu-btn' onClick={this.closeUserMenu}>
                    关闭
                  </Button>
                </View>
              </View>
            </View>
          )}
        </View>
      </View>
    )
  }
}
