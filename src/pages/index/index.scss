/* 首页 - 明亮风格，尽量贴合设计稿 */
.index {
  min-height: 100vh;
  position: relative;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 70vh;
  z-index: 0;
  transform: translateY(0rpx);
}

.home {
  padding: 40rpx 36rpx 260rpx;
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}


/* 品牌标题 */
.brand-block {
  text-align: center;
  margin-bottom: 48rpx;
}
.brand-en { font-size: 56rpx; font-weight: 800; color: #111; display: block; text-shadow: 0 2rpx 0 #fff; }
.brand-zh { font-size: 34rpx; font-weight: 800; color: #222; opacity: .88; margin-top: 8rpx; display: block; }

/* 四色功能卡片 2x2 */
.menu-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.menu-card {
  height: 180rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 700;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.08);
}
.menu-icon { font-size: 48rpx; line-height: 1; margin-bottom: 14rpx; }
.menu-title { font-size: 30rpx; }

.menu-card.orange { background: #f86d3e; }
.menu-card.purple { background: #7b56f6; }
.menu-card.green { background: #16c087; }
.menu-card.yellow { background: #f3bf3a; color: #2b2b2b; }

/* 底部横幅 */
.footer-banner {
  margin-top: 36rpx;
  //background: #fff7d6;
  color: #2b2b2b;
  border-radius: 16rpx;
  padding: 20rpx 24rpx 120rpx;
  text-align: center;
  font-size: 28rpx;
  text-decoration: underline;
  font-weight: 700;
  //border: 2rpx solid #ffe59a;
}

/* 保留并复用现有的用户菜单样式（颜色微调保持一致） */
.user-menu-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,.6); display:flex; align-items:center; justify-content:center; z-index:1001; padding:40rpx; box-sizing:border-box; }
.user-menu { width: 100%; max-width: 500rpx; background: #fff; border-radius: 20rpx; box-shadow: 0 20rpx 60rpx rgba(0,0,0,.2); }
.user-menu-content { padding: 50rpx 40rpx 40rpx; text-align: center; }
.user-info { margin-bottom: 40rpx; padding-bottom: 30rpx; border-bottom: 1px solid #f0f0f0; }
.menu-avatar { width: 120rpx; height: 120rpx; border-radius: 60rpx; margin-bottom: 20rpx; border: 3rpx solid #f0f0f0; }
.user-name { display: block; font-size: 36rpx; font-weight: 800; color: #111; margin-bottom: 10rpx; }
.user-phone { display: block; font-size: 28rpx; color: #666; }
.phone-btn, .logout-btn, .close-menu-btn { width:100%; border:none; border-radius: 30rpx; padding: 25rpx 0; font-size: 32rpx; font-weight: 700; margin-bottom: 20rpx; transition: all .2s ease; }
.phone-btn { background: #16c087; color: #fff; }
.logout-btn { background: #f86d3e; color: #fff; }
.close-menu-btn { background: #f2f2f2; color: #333; margin-bottom: 0; }
.phone-btn:active, .logout-btn:active, .close-menu-btn:active { transform: translateY(2rpx); }

/* 临时隐藏右上角头像入口（设计未体现） */
.user-avatar-container { display: none; }
