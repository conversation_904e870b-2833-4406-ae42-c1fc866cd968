import { Component } from 'react'
import { View, Text, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { Word, GameState, WrongWordResult } from '../../types/game'
import { getApiService } from '../../services/api'
import { voiceService } from '../../services/voice'
import { HistoryService } from '../../services/historyService'
import { audioService } from '../../services/audioService'
import { captureError, addBreadcrumb } from '../../utils/sentry'
import './index.scss'

interface GamePageState {
  words: Word[]
  gameState: GameState
  feedback: string
  isLoading: boolean
  wrongWords: WrongWordResult[]
  correctWords: Word[]
  currentUserAnswer: string
  historySaved: boolean
  gameMode: 'guess' | 'pronunciation' | 'endless' | 'endless-guess' | 'endless-pronunciation'
  elapsedTime: number
}

export default class Game extends Component<{}, GamePageState> {

  // 辅助函数：判断是否为无尽模式
  private isEndlessMode(mode: string): boolean {
    return Game.isEndlessMode(mode)
  }

  // 静态辅助函数：判断是否为无尽模式
  private static isEndlessMode(mode: string): boolean {
    return mode === 'endless' || mode === 'endless-guess' || mode === 'endless-pronunciation'
  }

  constructor(props) {
    super(props)
    this.state = {
      words: [],
      gameState: {
        currentWordIndex: 0,
        score: 0,
        totalWords: 0,
        isRecording: false,
        gameStatus: 'ready',
        attempts: 0
      },
      feedback: '',
      isLoading: true,
      wrongWords: [],
      correctWords: [],
      currentUserAnswer: '',
      historySaved: false,
      gameMode: 'guess',
      elapsedTime: 0
    }

    // 绑定方法到this
    this.getCorrectAnswerFromImage = this.getCorrectAnswerFromImage.bind(this)
    this.initGame = this.initGame.bind(this)
    this.getCurrentWord = this.getCurrentWord.bind(this)
    this.startRecording = this.startRecording.bind(this)
    this.stopRecording = this.stopRecording.bind(this)
    this.nextQuestion = this.nextQuestion.bind(this)
    this.restartGame = this.restartGame.bind(this)
    this.goToResult = this.goToResult.bind(this)
    this.startTimer = this.startTimer.bind(this)
    this.stopTimer = this.stopTimer.bind(this)
    this.formatTime = this.formatTime.bind(this)
    this.loadMoreWords = this.loadMoreWords.bind(this)
  }

  componentDidMount() {
    addBreadcrumb({
      message: '游戏页面加载',
      category: 'navigation',
      level: 'info'
    })

    // 启用分享功能
    Taro.showShareMenu({
      withShareTicket: true
    })

    // 获取URL参数中的游戏模式
    const instance = Taro.getCurrentInstance()
    const gameMode = instance.router?.params?.mode || 'guess'

    this.setState({ gameMode: gameMode as 'guess' | 'pronunciation' | 'endless' | 'endless-guess' | 'endless-pronunciation' }, () => {
      // 异步初始化游戏
      this.initGame().catch(error => {
        console.error('组件挂载时初始化游戏失败:', error)
      })
    })
  }

  componentWillUnmount() {
    // 清理所有定时器
    if (this.redirectTimer) {
      clearTimeout(this.redirectTimer)
      this.redirectTimer = null
    }
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
      this.timeTimer = null
    }
  }

  // 计时与跳转定时器（放在生命周期方法后以满足排序规则）
  private redirectTimer: any = null  // 保存定时器引用
  private timeTimer: any = null  // 计时器引用

  // 小程序页面显示时触发（Taro 生命周期）
  componentDidShow() {
    // 预检查录音权限，首次进入时触发授权弹窗
    voiceService
      .checkRecordPermission()
      .catch(err => {
        console.warn('预检查录音权限失败:', err)
      })
  }

  // 开始计时器
  startTimer() {
    this.timeTimer = setInterval(() => {
      const { gameState } = this.state
      if (gameState.startTime && gameState.gameStatus === 'playing') {
        const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000)
        this.setState({ elapsedTime: elapsed })
      }
    }, 1000)
  }

  // 停止计时器
  stopTimer() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
      this.timeTimer = null
    }
  }

  // 格式化时间显示 (mm:ss)
  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 为无尽模式加载更多单词
  async loadMoreWords(isLevelUpgrade: boolean = false, targetLevel?: number, insertAtCurrent: boolean = false) {
    try {
      const api = getApiService()
      const { gameMode, gameState } = this.state

      // 只有无尽模式才需要加载更多单词
      if (!this.isEndlessMode(gameMode)) {
        console.warn('loadMoreWords called for non-endless mode:', gameMode)
        return
      }

      // 根据游戏模式和当前难度等级获取对应单词
      let newWords
      const currentLevel = targetLevel || gameState.currentLevel || 1

      console.log(`🔍 loadMoreWords调用详情: targetLevel=${targetLevel}, gameState.currentLevel=${gameState.currentLevel}, finalLevel=${currentLevel}, isLevelUpgrade=${isLevelUpgrade}`)

      if (gameMode === 'endless-pronunciation') {
        // 每次都强制刷新确保获取不重复的新数据
        console.log(`📡 即将调用 fetchPronunciationOrderedByDifficulty(${currentLevel}, 5, true)`)
        newWords = await api.fetchPronunciationOrderedByDifficulty(currentLevel, 5, true)
        console.log(`✅ 无尽发音挑战模式: 加载Level${currentLevel}的单词${isLevelUpgrade ? '(升级后)' : '(继续游戏)'}, 获得${newWords.length}个单词`)
      } else if (gameMode === 'endless-guess') {
        newWords = await api.fetchRandomOrderedByDifficulty(currentLevel, 5)
        console.log(`✅ 无尽看图猜词模式: 加载Level${currentLevel}的单词${isLevelUpgrade ? '(升级后)' : '(继续游戏)'}, 获得${newWords.length}个单词`)
      } else {
        console.error('Unknown endless mode:', gameMode)
        return
      }

      // 随机打乱新获取的单词
      for (let i = newWords.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newWords[i], newWords[j]] = [newWords[j], newWords[i]]
      }
      console.log(`加载更多单词后打乱顺序 Level${currentLevel}:`, JSON.stringify(newWords.slice(0, 3).map(w => ({ word: w.word, level: w.level })), null, 2))

      // 预下载图片并缓存路径
      const optimizedNewWords = await Promise.all(newWords.map(async word => {
        try {
          const downloadResult = await Taro.downloadFile({
            url: word.image
          })

          return {
            id: word.id,
            word: word.word,
            image: word.image,
            tempImagePath: downloadResult.statusCode === 200 ? downloadResult.tempFilePath : undefined,
            meaning: word.meaning,
            pronunciation: word.pronunciation || '',
            level: word.level !== undefined ? word.level : 1 // 使用level字段，默认为1级
          }
        } catch (error) {
          console.warn(`预下载图片失败: ${word.image}`, error)
          return {
            id: word.id,
            word: word.word,
            image: word.image,
            tempImagePath: undefined,
            meaning: word.meaning,
            pronunciation: word.pronunciation || '',
            level: word.level !== undefined ? word.level : 1 // 使用level字段，默认为1级
          }
        }
      }))

      // 将新单词添加到现有单词列表中
      this.setState(prevState => {
        if (insertAtCurrent) {
          // 升级时：将新级别单词插入到当前位置之后，立即可用
          const currentIndex = prevState.gameState.currentWordIndex
          const newWords = [
            ...prevState.words.slice(0, currentIndex + 1),
            ...optimizedNewWords,
            ...prevState.words.slice(currentIndex + 1)
          ]
          console.log(`🔄 升级插入: 在位置${currentIndex + 1}插入${optimizedNewWords.length}个新Level${targetLevel}单词`)
          return { words: newWords }
        } else {
          // 正常情况：追加到末尾
          console.log(`➕ 正常追加: 在末尾添加${optimizedNewWords.length}个单词`)
          return { words: [...prevState.words, ...optimizedNewWords] }
        }
      })

      addBreadcrumb({
        message: '无尽模式加载更多单词成功',
        category: 'game',
        level: 'info',
        data: { newWordsCount: optimizedNewWords.length }
      })
    } catch (error) {
      console.error('加载更多单词失败:', error)
      captureError(error as Error, {
        context: 'loadMoreWords'
      })
    }
  }

  // 从图片URL中提取正确答案
  getCorrectAnswerFromImage(imageUrl: string): string {
    try {
      if (!imageUrl || typeof imageUrl !== 'string') {
        console.error('无效的图片URL:', imageUrl)
        return 'unknown'
      }

      // 直接从当前游戏单词中查找，避免获取所有单词数据
      const { words } = this.state

      // 先在当前游戏单词中查找
      for (let i = 0; i < words.length; i++) {
        const word = words[i]
        if (word.image === imageUrl) {
          return word.word
        }
      }

      // 如果完全匹配没找到，尝试去掉URL参数后再匹配
      const cleanUrl = imageUrl.split('?')[0]
      for (let i = 0; i < words.length; i++) {
        const word = words[i]
        const cleanWordUrl = word.image.split('?')[0]
        if (cleanWordUrl === cleanUrl) {
          return word.word
        }
      }

      // 如果在当前游戏单词中找不到，返回 unknown
      return 'unknown'
    } catch (error) {
      console.error('查找单词失败:', error)
      captureError(error as Error, {
        context: 'getCorrectAnswerFromImage',
        imageUrl,
        wordsCount: this.state.words.length
      })
      return 'unknown'
    }
  }

  async initGame() {
    try {
      this.setState({ isLoading: true, feedback: '' })

      addBreadcrumb({
        message: '开始初始化游戏',
        category: 'game',
        level: 'info'
      })

      // 根据游戏模式获取不同的单词
      const { gameMode } = this.state
      let gameWords
      if (this.isEndlessMode(gameMode)) {
        // 无尽模式：一次取50个level 1的单词，避免重复
        if (gameMode === 'endless-pronunciation') {
          gameWords = await getApiService().fetchPronunciationOrderedByDifficulty(1, 50) // 无尽发音挑战：从level 1开始，一次50个
        } else {
          gameWords = await getApiService().fetchRandomOrderedByDifficulty(1, 50) // 无尽看图猜词：从level 1开始，一次50个
        }
      } else if (gameMode === 'pronunciation') {
        // 普通发音挑战模式：取level 1-5各1个单词，总共5个
        gameWords = await getApiService().fetchOrderedByDifficulty() // 获取所有难度的单词，然后筛选
      } else {
        // 普通看图猜词模式：取level 1-5各1个单词，总共5个
        gameWords = await getApiService().fetchOrderedByDifficulty() // 获取所有难度的单词，然后筛选
      }

      // 普通模式需要筛选：从每个level取1个单词
      if (!this.isEndlessMode(gameMode)) {
        const wordsByLevel = new Map<number, Word[]>()

        // 按level分组
        gameWords.forEach(word => {
          const level = word.level || 1
          if (!wordsByLevel.has(level)) {
            wordsByLevel.set(level, [])
          }
          wordsByLevel.get(level)!.push(word)
        })

        // 从每个level取1个单词，level 1-5
        const selectedWords: Word[] = []
        for (let level = 1; level <= 5; level++) {
          const wordsAtLevel = wordsByLevel.get(level) || []
          if (wordsAtLevel.length > 0) {
            // 随机选一个
            const randomIndex = Math.floor(Math.random() * wordsAtLevel.length)
            selectedWords.push(wordsAtLevel[randomIndex])
          }
        }

        gameWords = selectedWords
        console.log('普通模式筛选后的单词（每个level 1个）:', JSON.stringify(gameWords.map(w => ({ word: w.word, level: w.level })), null, 2))
      }

      // 调试：检查从服务端获取的单词数据
      console.log('从服务端获取的原始单词数据（前3个）:', JSON.stringify(gameWords.slice(0, 3), null, 2))

      // 只有无尽模式才随机打乱单词顺序
      if (this.isEndlessMode(gameMode)) {
        for (let i = gameWords.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [gameWords[i], gameWords[j]] = [gameWords[j], gameWords[i]]
        }
        console.log('无尽模式 - 随机打乱后的单词顺序（前3个）:', JSON.stringify(gameWords.slice(0, 3).map(w => ({ word: w.word, level: w.level })), null, 2))
      } else {
        console.log('普通模式 - 保持原有单词顺序（前3个）:', JSON.stringify(gameWords.slice(0, 3).map(w => ({ word: w.word, level: w.level })), null, 2))
      }

      // 只保留必要的单词数据，减少内存占用，并预下载图片
      const optimizedWords = await Promise.all(gameWords.map(async word => {
        try {
          // 预下载图片并缓存路径
          const downloadResult = await Taro.downloadFile({
            url: word.image
          })

          const optimizedWord = {
            id: word.id,
            word: word.word,
            image: word.image,
            tempImagePath: downloadResult.statusCode === 200 ? downloadResult.tempFilePath : undefined,
            meaning: word.meaning,
            pronunciation: word.pronunciation || '', // 兼容可能缺失的音标字段
            level: word.level !== undefined ? word.level : 1 // 使用level字段，默认为1级
          }

          console.log(`单词 "${word.word}" 的难度: level=${word.level}, 处理后=${optimizedWord.level}`)

          return optimizedWord
        } catch (error) {
          console.warn(`预下载图片失败: ${word.image}`, error)
          const optimizedWord = {
            id: word.id,
            word: word.word,
            image: word.image,
            tempImagePath: undefined,
            meaning: word.meaning,
            pronunciation: word.pronunciation || '', // 兼容可能缺失的音标字段
            level: word.level !== undefined ? word.level : 1 // 使用level字段，默认为1级
          }

          console.log(`单词 "${word.word}" 的难度（图片下载失败）: level=${word.level}, 处理后=${optimizedWord.level}`)

          return optimizedWord
        }
      }))

      // 初始化游戏状态，无尽模式需要特殊处理
      const isEndlessMode = this.isEndlessMode(gameMode)
      const isPronunciationMode = gameMode === 'pronunciation' || gameMode === 'endless-pronunciation'
      const initialGameState = {
        currentWordIndex: 0,
        score: 0,
        totalWords: isEndlessMode ? 0 : optimizedWords.length, // 无尽模式总数为0
        isRecording: false,
        gameStatus: 'playing' as const,
        attempts: 0,
        startTime: Date.now(),
        ...(isEndlessMode && {
          lives: 5, // 无尽模式初始5颗心
          correctStreak: 0 // 连续答对次数
        }),
        ...(isEndlessMode && {
          currentLevel: 1, // 无尽模式从level 1开始
          currentBatchScore: 0 // 当前批次答对题数
        })
      }

      this.setState({
        words: optimizedWords,
        gameState: initialGameState,
        feedback: '',
        isLoading: false,
        wrongWords: [],
        currentUserAnswer: '',
        historySaved: false,
        elapsedTime: 0
      })

      // 启动计时器
      this.startTimer()

      addBreadcrumb({
        message: '游戏初始化成功',
        category: 'game',
        level: 'info',
        data: { totalWords: optimizedWords.length }
      })
    } catch (error) {
      console.error('初始化游戏失败:', error)
      captureError(error as Error, {
        context: 'initGame'
      })
      this.setState({
        feedback: '游戏初始化失败，请重试',
        isLoading: false
      })
    }
  }

  getCurrentWord(): Word | null {
    const { words, gameState } = this.state
    if (words.length === 0 || gameState.currentWordIndex >= words.length) {
      return null
    }
    return words[gameState.currentWordIndex]
  }

  async startRecording() {
    try {
      addBreadcrumb({
        message: '开始录音',
        category: 'recording',
        level: 'info'
      })

      // 检查录音权限
      const hasPermission = await voiceService.checkRecordPermission()
      if (!hasPermission) {
        Taro.showToast({
          title: '需要录音权限才能使用语音识别',
          icon: 'none'
        })
        return
      }

      const self = this
      self.setState(prevState => ({
        gameState: {
          ...prevState.gameState,
          isRecording: true
        },
        feedback: '正在录音...'
      }))

      await voiceService.startRecord()
    } catch (error) {
      console.error('开始录音失败:', error)
      captureError(error as Error, {
        context: 'startRecording',
        currentWordIndex: this.state.gameState.currentWordIndex
      })
      const self = this
      self.setState(prevState => ({
        gameState: {
          ...prevState.gameState,
          isRecording: false
        },
        feedback: '录音失败，请重试'
      }))
    }
  }

  async stopRecording() {
    try {
      const audioPath = await voiceService.stopRecord()
      const self = this
      self.setState(prevState => ({
        gameState: {
          ...prevState.gameState,
          isRecording: false
        },
        feedback: '正在识别...'
      }))

      const currentWord = this.getCurrentWord()
      if (!currentWord) return

      // 发送到后端进行ASR识别
      // 将无尽模式映射为基础模式，ASR 仅区分 guess 与 pronunciation
      const asrMode: 'guess' | 'pronunciation' = (this.state.gameMode === 'pronunciation' || this.state.gameMode === 'endless-pronunciation')
        ? 'pronunciation'
        : 'guess'
      const result = await voiceService.sendToASR(audioPath, currentWord.word, asrMode)

      // 直接使用 VoiceService 返回的 success 判断（已经基于置信度判断过了）
      const isSuccess = result && result.success

      // 记录用户的回答
      const userAnswer = result ? result.text : ''
      this.setState({ currentUserAnswer: userAnswer })

      if (isSuccess) {
        // 识别正确
        this.setState({ feedback: '恭喜答对了！' })

        // 播放答对音效
        audioService.playCorrectSound().catch(error => {
          console.warn('播放答对音效失败:', error)
        })

        // 记录答对的单词
        this.setState(prevState => ({
          correctWords: [...prevState.correctWords, currentWord]
        }))

        setTimeout(function() {
          const { gameState, words, gameMode } = self.state
          const nextIndex = gameState.currentWordIndex + 1

          // 更新分数和连续答对次数
          let newScore = gameState.score + 1
          let newCorrectStreak = (gameState.correctStreak || 0) + 1
          let newLives = gameState.lives || 0
          // 无尽模式：难度递进逻辑
          const isEndlessMode = Game.isEndlessMode(gameMode)
          let needsLevelUp = false
          let newCurrentLevel = gameState.currentLevel || 1
          let newCurrentBatchScore = (gameState.currentBatchScore || 0) + 1

          if (isEndlessMode) {
            // 当前批次答对3个以上且还没到最高级别时升级
            if (newCurrentBatchScore >= 3 && newCurrentLevel < 5) {
              needsLevelUp = true
              newCurrentLevel = Math.min(newCurrentLevel + 1, 5)
              newCurrentBatchScore = 0 // 重置批次分数
              self.setState({ feedback: `太棒了！升级到 Level ${newCurrentLevel}！` })
            }
          }

          // 无尽模式：连续答对5个词增加1颗心
          if (Game.isEndlessMode(gameMode) && newCorrectStreak >= 5) {
            newLives = Math.min(newLives + 1, 5) // 最多5颗心
            newCorrectStreak = 0 // 重置连续答对次数
            if (!needsLevelUp) { // 如果没有升级消息，显示加心消息
              self.setState({ feedback: '恭喜！连续答对5题， +1！' })
            }
          }

          // 检查是否需要结束游戏
          const shouldEndGame = Game.isEndlessMode(gameMode)
            ? false // 无尽模式不会因为题目用完而结束
            : nextIndex >= words.length

          if (shouldEndGame) {
            // 游戏完成（非无尽模式）

            // 播放完成庆祝音效
            audioService.playCompletionSound().catch(error => {
              console.warn('播放完成音效失败:', error)
            })

            self.setState(prevState => ({
              gameState: {
                ...prevState.gameState,
                currentWordIndex: nextIndex,
                score: newScore,
                gameStatus: 'completed',
                endTime: Date.now()
              }
            }))

            // 停止计时器
            self.stopTimer()

            // 直接跳转到结果页面，不显示弹窗
            setTimeout(() => {
              self.goToResult()
            }, 1000) // 减少延迟时间
          } else {
            // 下一个单词
            self.setState(prevState => ({
              gameState: {
                ...prevState.gameState,
                currentWordIndex: nextIndex,
                score: newScore,
                attempts: 0,
                correctStreak: newCorrectStreak,
                lives: newLives,
                ...(isEndlessMode && {
                  currentLevel: newCurrentLevel,
                  currentBatchScore: newCurrentBatchScore
                })
              },
              feedback: Game.isEndlessMode(gameMode) && newCorrectStreak === 0 && newLives > (gameState.lives || 0)
                ? '恭喜！连续答对5题'
                : '',
              currentUserAnswer: ''
            }))

            // 无尽模式：当剩余单词不足3个时，自动加载更多单词
            if (Game.isEndlessMode(gameMode) && nextIndex >= self.state.words.length - 3) {
              self.loadMoreWords().catch(error => {
                console.error('自动加载更多单词失败:', error)
              })
            }

            // 无尽模式：如果升级了，立即加载新等级的单词
            if (Game.isEndlessMode(gameMode) && needsLevelUp) {
              console.log(`🚀 升级检测: needsLevelUp=${needsLevelUp}, newCurrentLevel=${newCurrentLevel}, 即将调用loadMoreWords(true, ${newCurrentLevel}, true)`)
              self.loadMoreWords(true, newCurrentLevel, true).catch(error => {
                console.error('升级后加载新等级单词失败:', error)
              })
            }
          }
        }, 2000) // 改为2秒
      } else {
        // 识别错误
        const newAttempts = this.state.gameState.attempts + 1
        console.log(`发音识别失败，当前尝试次数: ${this.state.gameState.attempts} -> ${newAttempts}`)

        // 根据游戏模式处理错误
        const { gameMode } = this.state

        if (this.isEndlessMode(gameMode)) {
          // 无尽模式：答错扣0.5颗心，重置连续答对次数
          const currentLives = this.state.gameState.lives || 0
          const newLives = Math.max(0, currentLives - 0.5)

          // 播放答错音效
          audioService.playErrorSound().catch(error => {
            console.warn('播放答错音效失败:', error)
          })

          // 重置连续答对次数
          self.setState(prevState => ({
            feedback: `答错了 (${newLives}/5)`,
            gameState: {
              ...prevState.gameState,
              attempts: newAttempts,
              isRecording: false,
              lives: newLives,
              correctStreak: 0 // 重置连续答对次数
            },
            currentUserAnswer: userAnswer
          }))

          // 检查生命值是否耗尽
          if (newLives <= 0) {
            // 游戏结束
            setTimeout(() => {
              // 播放完成庆祝音效
              audioService.playCompletionSound().catch(error => {
                console.warn('播放完成音效失败:', error)
              })

              self.setState(prevState => ({
                gameState: {
                  ...prevState.gameState,
                  gameStatus: 'completed',
                  endTime: Date.now()
                },
                feedback: '游戏结束！'
              }))

              // 停止计时器
              self.stopTimer()

              // 直接跳转到结果页面，不显示弹窗
              setTimeout(() => {
                self.goToResult()
              }, 1000)
            }, 2000)
          } else {
            // 2秒后自动进入下一个单词
            setTimeout(() => {
              const nextIndex = self.state.gameState.currentWordIndex + 1
              self.setState(prevState => ({
                gameState: {
                  ...prevState.gameState,
                  currentWordIndex: nextIndex,
                  attempts: 0
                },
                feedback: '',
                currentUserAnswer: ''
              }))

              // 无尽模式：当剩余单词不足3个时，自动加载更多单词
              if (nextIndex >= self.state.words.length - 3) {
                self.loadMoreWords().catch(error => {
                  console.error('自动加载更多单词失败:', error)
                })
              }
            }, 2000)
          }
        } else {
          // 原有的其他模式逻辑
          const maxAttempts = gameMode === 'pronunciation' ? Infinity : 3

          if (newAttempts >= maxAttempts) {
            // 失败3次，显示正确答案并自动进入下一个单词
            const correctAnswer = this.getCorrectAnswerFromImage(currentWord.image)
            console.log(`失败3次，显示正确答案: ${correctAnswer}`)

            // 播放答错音效
            audioService.playErrorSound().catch(error => {
              console.warn('播放答错音效失败:', error)
            })

            // 将错误单词添加到 wrongWords 数组
            const wrongWordResult: WrongWordResult = {
              word: currentWord,
              userAnswer: this.state.currentUserAnswer,
              attempts: newAttempts
            }

            self.setState(prevState => ({
              feedback: `正确答案是: ${correctAnswer}`,
              gameState: {
                ...prevState.gameState,
                attempts: newAttempts,
                isRecording: false
              },
              wrongWords: [...prevState.wrongWords, wrongWordResult]
            }))

            // 2秒后自动进入下一个单词
            setTimeout(function() {
              self.setState(prevState => {
                const nextIndex = prevState.gameState.currentWordIndex + 1

                if (nextIndex >= prevState.words.length) {
                  // 游戏完成

                  // 播放完成庆祝音效
                  audioService.playCompletionSound().catch(error => {
                    console.warn('播放完成音效失败:', error)
                  })

                  // 停止计时器
                  self.stopTimer()

                  self.redirectTimer = setTimeout(() => {
                    self.goToResult()
                  }, 3000)

                  return {
                    ...prevState,
                    gameState: {
                      ...prevState.gameState,
                      currentWordIndex: nextIndex,
                      gameStatus: 'completed',
                      endTime: Date.now()
                    }
                  }
                } else {
                  // 下一个单词
                  return {
                    ...prevState,
                    gameState: {
                      ...prevState.gameState,
                      currentWordIndex: nextIndex,
                      attempts: 0
                    },
                    feedback: '',
                    currentUserAnswer: ''
                  }
                }
              })
            }, 2000)
          } else {
            // 更新尝试次数并显示反馈
            console.log(`更新尝试次数为: ${newAttempts}`)

            // 播放答错音效
            audioService.playErrorSound().catch(error => {
              console.warn('播放答错音效失败:', error)
            })

            const feedbackMessage = gameMode === 'pronunciation'
              ? '发音不太对，再试试看！'
              : `发音不太对，再试试看！(${newAttempts}/3)`

            self.setState(prevState => ({
              feedback: feedbackMessage,
              gameState: {
                ...prevState.gameState,
                attempts: newAttempts,
                isRecording: false
              },
              currentUserAnswer: userAnswer
            }))
          }
        }
      }
    } catch (error) {
      console.error('语音识别失败:', error)
      captureError(error as Error, {
        context: 'stopRecording',
        currentWordIndex: this.state.gameState.currentWordIndex,
        attempts: this.state.gameState.attempts
      })
      const self = this
      self.setState(prevState => ({
        gameState: {
          ...prevState.gameState,
          isRecording: false
        },
        feedback: '识别失败，请重试'
      }))
    }
  }

  nextQuestion() {
    const { gameState, words, gameMode } = this.state
    const nextIndex = gameState.currentWordIndex + 1

    // 将当前题目记录为错误题目
    const currentWord = this.getCurrentWord()
    if (currentWord) {
      const wrongWordResult: WrongWordResult = {
        word: currentWord,
        userAnswer: this.state.currentUserAnswer || '跳过',
        attempts: this.state.gameState.attempts
      }

      this.setState(prevState => ({
        wrongWords: [...prevState.wrongWords, wrongWordResult]
      }))
    }

    if (this.isEndlessMode(gameMode)) {
      // 无尽模式：跳过扣1颗心
      const currentLives = gameState.lives || 0
      const newLives = Math.max(0, currentLives - 1)

      this.setState(prevState => ({
        gameState: {
          ...prevState.gameState,
          currentWordIndex: nextIndex,
          attempts: 0,
          lives: newLives,
          correctStreak: 0 // 重置连续答对次数
        },
        feedback: `跳过 (${newLives}/5)`,
        currentUserAnswer: ''
      }))

      // 检查生命值是否耗尽
      if (newLives <= 0) {
        // 游戏结束
        setTimeout(() => {
          // 播放完成庆祝音效
          audioService.playCompletionSound().catch(error => {
            console.warn('播放完成音效失败:', error)
          })

          this.setState(prevState => ({
            gameState: {
              ...prevState.gameState,
              gameStatus: 'completed',
              endTime: Date.now()
            },
            feedback: '游戏结束！'
          }))

          // 停止计时器
          this.stopTimer()

          // 直接跳转到结果页面，不显示弹窗
          setTimeout(() => {
            this.goToResult()
          }, 1000)
        }, 2000)
      } else {
        // 清除反馈消息
        setTimeout(() => {
          this.setState({ feedback: '' })

          // 无尽模式：当剩余单词不足3个时，自动加载更多单词
          if (nextIndex >= this.state.words.length - 3) {
            this.loadMoreWords().catch(error => {
              console.error('自动加载更多单词失败:', error)
            })
          }
        }, 2000)
      }
    } else {
      // 原有的其他模式逻辑
      if (nextIndex >= words.length) {
        // 游戏完成

        // 播放完成庆祝音效
        audioService.playCompletionSound().catch(error => {
          console.warn('播放完成音效失败:', error)
        })

        // 停止计时器
        this.stopTimer()

        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            currentWordIndex: nextIndex,
            gameStatus: 'completed',
            endTime: Date.now()
          },
          feedback: ''
        }))

        // 直接跳转到结果页面，不显示弹窗
        setTimeout(() => {
          this.goToResult()
        }, 1000)
      } else {
        // 下一个单词
        this.setState(prevState => ({
          gameState: {
            ...prevState.gameState,
            currentWordIndex: nextIndex,
            attempts: 0
          },
          feedback: '',
          currentUserAnswer: ''
        }))
      }
    }
  }

  restartGame() {
    // 停止当前计时器
    this.stopTimer()

    // 异步重新初始化游戏
    this.initGame().catch(error => {
      console.error('重新开始游戏失败:', error)
    })
  }

  async goToResult() {
    // 清理定时器
    if (this.redirectTimer) {
      clearTimeout(this.redirectTimer)
      this.redirectTimer = null
    }

    const { gameState, wrongWords, correctWords, historySaved } = this.state
    const duration = gameState.endTime && gameState.startTime
      ? gameState.endTime - gameState.startTime
      : 0

    // 只在第一次调用时保存游戏历史记录
    if (!historySaved) {
      try {
        // 历史记录仅保存 guess/pronunciation 两类模式
        const historyMode: 'guess' | 'pronunciation' = (this.state.gameMode === 'pronunciation' || this.state.gameMode === 'endless-pronunciation')
          ? 'pronunciation'
          : 'guess'
        await HistoryService.saveGameHistory(
          gameState.score,
          gameState.totalWords,
          duration,
          wrongWords,
          historyMode
        )
        this.setState({ historySaved: true })
      } catch (error) {
        console.error('保存游戏历史记录失败:', error)
      }
    }

    // 将错误单词和正确单词数据编码为URL参数
    const wrongWordsParam = encodeURIComponent(JSON.stringify(wrongWords))
    const correctWordsParam = encodeURIComponent(JSON.stringify(correctWords))

    // 根据您的建议，设置正确的gameMode参数
    let resultGameMode = this.state.gameMode
    if (this.state.gameMode === 'endless-guess') {
      resultGameMode = 'endless-guess'
    } else if (this.state.gameMode === 'endless-pronunciation') {
      resultGameMode = 'endless-pronunciation'
    }

    Taro.redirectTo({
      url: `/packages/features/pages/result/index?score=${gameState.score}&total=${gameState.totalWords}&duration=${duration}&wrongWords=${wrongWordsParam}&correctWords=${correctWordsParam}&mode=${resultGameMode}`
    })
  }

  // 配置分享功能 - 当用户点击右上角分享按钮时触发
  onShareAppMessage() {
    const currentWord = this.getCurrentWord()
    if (!currentWord) {
      return {
        title: '我在玩发音挑战',
        path: '/pages/index/index'
      }
    }

    // 使用单词图片作为分享图片
    return {
      title: `这个单词怎么读？${currentWord.word}`,
      path: `/pages/index/index`,
      imageUrl: currentWord.image
    }
  }

  // 分享到朋友圈
  onShareTimeline() {
    const currentWord = this.getCurrentWord()
    if (!currentWord) {
      return {
        title: '发音挑战 - 提升英语口语'
      }
    }

    return {
      title: `这个单词怎么读？${currentWord.word}`,
      imageUrl: currentWord.image
    }
  }

  render() {
    const { gameState, feedback, isLoading } = this.state
    const currentWord = this.getCurrentWord()

    // 如果正在加载，显示加载状态
    if (isLoading) {
      return (
        <View className='game-container'>
          <View className='loading-state'>
            <Text className='loading-text'>🎮 正在加载游戏...</Text>
            <Text className='loading-tip'>从服务器获取题目中，请稍候</Text>
          </View>
        </View>
      )
    }

    // 如果游戏已完成，显示简单的完成状态
    if (gameState.gameStatus === 'completed') {
      return (
        <View className='game-container'>
          <View className='completed-state'>
            <Text className='completed-text'>🎉 游戏完成！即将跳转到结果页面...</Text>
          </View>
        </View>
      )
    }

    if (!currentWord) {
      return (
        <View className='game-container'>
          <Text>加载中...</Text>
        </View>
      )
    }

    const isPronunciationMode = this.state.gameMode === 'pronunciation' || this.state.gameMode === 'endless-pronunciation'

    return (
      <View className={`game-container ${isPronunciationMode ? 'pronunciation-mode' : ''}`}>
        <View className='game-title'>
          <Text className='title-text'>
            {isPronunciationMode ? '发音挑战' : '看图猜词'}
          </Text>
        </View>
        <View className='header'>
          <View className='header-row'>
            {this.isEndlessMode(this.state.gameMode) ? (
              <Text className='progress'>第 {gameState.currentWordIndex + 1} 题</Text>
            ) : (
              <Text className='progress'>进度: {gameState.currentWordIndex + 1}/{gameState.totalWords}</Text>
            )}
            <Text className='timer'>⏰ {this.formatTime(this.state.elapsedTime)}</Text>
          </View>
          <View className='header-row'>
            <Text className='score'>得分: {gameState.score}</Text>
            {this.isEndlessMode(this.state.gameMode) ? (
              <View className='lives-display'>
                <Text className='lives-label'> </Text>
                {[1, 2, 3, 4, 5].map(i => (
                  <Text key={i} className='heart'>
                    {(gameState.lives || 0) >= i ? '❤️' :
                     (gameState.lives || 0) >= i - 0.5 ? '💔' : '🤍'}
                  </Text>
                ))}
              </View>
            ) : (
              <Text className='attempts'>
                尝试: {gameState.attempts}{this.state.gameMode === 'pronunciation' ? '' : '/3'}
              </Text>
            )}
          </View>
          {this.isEndlessMode(this.state.gameMode) && (
            <View className='header-row'>
              <Text className='streak'>连续答对: {gameState.correctStreak || 0}/5</Text>
              <Text className='endless-tip'>连续答对5题获得1颗心</Text>
            </View>
          )}
          {Game.isEndlessMode(this.state.gameMode) && (
            <View className='header-row'>
              <Text className='level-display'>难度: Level {gameState.currentLevel || 1}</Text>
              <Text className='batch-score'>本轮答对: {gameState.currentBatchScore || 0}/3</Text>
            </View>
          )}
        </View>

        {feedback && (
          <View className='feedback'>
            <Text className='feedback-text'>{feedback}</Text>
          </View>
        )}


        <View className='word-display'>
          <Image
            className='word-image'
            src={currentWord.image}
            mode='aspectFit'
          />
          {/* 难度等级显示 */}
          {currentWord.level !== undefined && (
            <View className='difficulty-indicator'>
              <Text className='difficulty-label'>难度:</Text>
              <View className='difficulty-stars'>
                {[1, 2, 3, 4, 5].map(level => (
                  <Text key={level} className={`star ${level <= currentWord.level! ? 'active' : ''}`}>
                    ⭐
                  </Text>
                ))}
              </View>
              <Text className='difficulty-debug'>({currentWord.level}级)</Text>
            </View>
          )}
          {/* 根据游戏模式显示不同内容 */}
          <Text className='word-text'>
            {(this.state.gameMode === 'pronunciation' || this.state.gameMode === 'endless-pronunciation') ? currentWord.word : currentWord.meaning}
          </Text>
        </View>

        <View className='controls'>
          <Button
            className={`record-btn ${gameState.isRecording ? 'recording' : ''}`}
            onTouchStart={this.startRecording}
            onTouchEnd={this.stopRecording}
            disabled={gameState.isRecording}
          >
            🎤
          </Button>

          {/* 按钮行 */}
          <View className='button-row'>
            <Button
              className='share-btn'
              openType='share'
              style={{ border: 'none', outline: 'none', boxShadow: 'none' }}
            >
              求助朋友
            </Button>

            <Button
              className='next-btn'
              onClick={this.nextQuestion}
              style={{ border: 'none', outline: 'none', boxShadow: 'none' }}
            >
              下一题
            </Button>
          </View>

        </View>
      </View>
    )
  }
}
