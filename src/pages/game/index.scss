.game-container {
  padding: calc(env(safe-area-inset-top) + 40rpx) 40rpx 40rpx;
  min-height: 100vh;
  display: flex;
  background: linear-gradient(to bottom, #97dbbf 0%, #FFFFFF 40%, #FFFFFF 100%) !important;
  flex-direction: column;
  position: relative;
}

.game-container.pronunciation-mode {
  background: linear-gradient(to bottom, #ffb2a0 0%, #FFFFFF 40%, #FFFFFF 100%) !important;
}

.game-container .controls,
.game-container .feedback {
  position: relative;
}

.game-title {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 100px;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 60rpx;
  padding: 0;
  padding-top: 20rpx;
  color: #666;
}

.header-row {
  //padding-top: 120rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.progress,
.score,
.attempts,
.timer {
  font-size: 24rpx;
  font-weight: normal;
  color: #666;
}

.attempts {
  color: #FF9500;
}

.lives-display {
  display: flex;
  align-items: center;
  gap: 5px;
}

.lives-label {
  font-size: 24rpx;
  font-weight: normal;
  color: #666;
}

.heart {
  font-size: 24rpx;
  margin: 0 2rpx;
}

.streak {
  font-size: 24rpx;
  font-weight: normal;
  color: #34C759;
}

.endless-tip {
  font-size: 20rpx;
  color: #8E8E93;
}

.level-display {
  font-size: 24rpx;
  font-weight: bold;
  color: #FF6B35;
}

.batch-score {
  font-size: 20rpx;
  color: #34C759;
}

.timer {
  color: #666;
}

.word-display {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.word-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 24rpx;
  margin-top: 40rpx;
  background: #f5f5f5;
  border: 4rpx solid #e0e0e0;
}

.word-text {
  font-size: 45rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  margin-left: 100px;
  margin-right: 100px;
  text-align: center;
}

.difficulty-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.difficulty-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.difficulty-stars {
  display: flex;
  align-items: center;
  gap: 2rpx;
}

.star {
  font-size: 20rpx;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.star.active {
  opacity: 1;
}

.difficulty-debug {
  font-size: 20rpx;
  color: #999;
  margin-left: 8rpx;
}

.word-pronunciation {
  font-size: 22px;
  color: rgba(255, 255, 255, 0.9);
}

.controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  margin-top: auto;
  height: 330px;
  margin-bottom: 40rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 60rpx);
}

.button-row {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.record-btn {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-bottom: 80rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF3B30 100%);
  color: white;
  font-size: 48rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 59, 48, 0.3);
  transition: all 0.3s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-btn .btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.record-btn .mic-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
  animation: pulse 2s infinite;
}


.record-btn.recording {
  background: linear-gradient(135deg, #FF3B30 0%, #CC0000 100%);
  transform: scale(1.05);
  box-shadow: 0 12rpx 40rpx rgba(255, 59, 48, 0.4);
}

.record-btn.recording .mic-icon {
  animation: recording-pulse 0.8s infinite;
}

.record-btn:active {
  transform: scale(0.95);
}

.record-btn:hover {
  box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
}

/* 下一题按钮 */
.next-btn {
  min-width: 160rpx;
  height: 56rpx;
  padding: 0 20rpx;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: #000000;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.next-btn::after {
  border: none !important;
}

.next-btn:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.next-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.next-btn:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 权限帮助按钮 */
.help-btn {
  min-width: 200px;
  height: 42px;
  padding: 0 16px;
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.35);
  color: #fff;
  font-size: 16px;
  backdrop-filter: blur(4px);
}

.help-btn:active {
  opacity: 0.85;
}

/* 分享按钮 */
.share-btn {
  min-width: 160rpx;
  height: 56rpx;
  padding: 0 20rpx;
  border-radius: 0;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: #000000;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.share-btn::after {
  border: none !important;
}

.share-btn:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.share-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.share-btn:hover {
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* 添加动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes recording-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.feedback {
  text-align: center;
  margin-bottom: 60rpx;
}

.feedback-text {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  padding: 40rpx 50rpx;
  background: #F2F2F7;
  border-radius: 30rpx;
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* 祝贺弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.congrats-modal {
  background: white;
  border-radius: 15px;
  padding: 30px;
  width: 80%;
  max-width: 350px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.congrats-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.congrats-score {
  font-size: 24px;
  color: #1e3c72;
  margin-bottom: 20px;
  font-weight: bold;
}

.congrats-text {
  font-size: 18px;
  color: #666;
  margin-bottom: 25px;
  line-height: 1.5;
}

/* 客服二维码区域 */
.qr-section {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
}

.qr-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  font-weight: bold;
}

.qr-code {
  width: 150px;
  height: 150px;
  margin: 0 auto 15px;
}

.qr-tip {
  font-size: 14px;
  color: #666;
}

.modal-buttons {
  display: flex;
  gap: 15px;
}

.restart-btn,
.result-btn {
  flex: 1;
  height: 45px;
  border-radius: 8px;
  border: none;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}

.restart-btn {
  background: #1e3c72;
  color: white;
}

.result-btn {
  background: #50C878;
  color: white;
}

/* 游戏完成状态样式 */
.completed-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.completed-text {
  font-size: 24px;
  color: white;
  font-weight: bold;
  text-align: center;
}

/* 加载状态样式 */
.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 36px;
  color: white;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15px;
  animation: loading-pulse 1.5s infinite;
}

.loading-tip {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.auto-jump-tip {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
  text-align: center;
  font-style: italic;
}
