@import './_tokens.scss';
// Base button system
.btn {position:relative;display:inline-flex;align-items:center;justify-content:center;font-weight:600;border:none;cursor:pointer;transition:background $duration-base $ease-standard,box-shadow $duration-base $ease-standard,transform $duration-fast $ease-standard;user-select:none;line-height:1;white-space:nowrap;}
.btn--full {width:100%;}
.btn--lg {height:96rpx;padding:0 $space-10;font-size:$font-md;border-radius:$radius-pill;}
.btn--md {height:72rpx;padding:0 $space-8;font-size:$font-sm;border-radius:$radius-pill;}
.btn--sm {height:56rpx;padding:0 $space-6;font-size:$font-sm;border-radius:$radius-pill;}

// Variants
.btn--gradient-accent {background:$gradient-accent;color:$color-text-inverse;@include elevation(md);&:active{transform:translateY(2rpx);@include elevation(sm);} }
.btn--gradient-pink {background:$gradient-pink;color:$color-text-inverse;@include elevation(md);&:active{transform:translateY(2rpx);@include elevation(sm);} }
.btn--outline-glass { @include glass(); color:$color-text-inverse; &::after{content:'';position:absolute;inset:0;border-radius:inherit;opacity:0;transition:opacity $duration-fast $ease-standard;background:rgba(255,255,255,.15);} &:active::after{opacity:1;} }
.btn--ghost {background:transparent;color:$color-text-inverse;}
.btn--danger {background:$gradient-danger;color:$color-text-inverse;@include elevation(md);&:active{transform:translateY(2rpx);@include elevation(sm);} }
.btn--success {background:$gradient-success;color:$color-text-inverse;@include elevation(md);&:active{transform:translateY(2rpx);@include elevation(sm);} }

// Subtle press effect for any button
.btn:active {filter:saturate(.95);}
