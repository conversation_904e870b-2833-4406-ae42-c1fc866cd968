// Design Tokens v1 — 基础设计体系集中化
// 后续演进：暗色模式 / 自适应字号 / 语义分层扩展 / 可访问性状态色

// 基础色板
$indigo-500: #6366f1; $indigo-600: #4f46e5; $violet-600: #6d28d9;
$pink-500: #ec4899; $pink-600: #db2777; $green-500: #22c55e; $green-600: #16a34a;
$gold-400: #facc15; $red-500: #ef4444; $red-600: #dc2626;
$slate-50: #f8fafc; $slate-100: #f1f5f9; $slate-200: #e2e8f0; $slate-400: #94a3b8; $slate-500: #64748b; $slate-600: #475569; $slate-700: #334155;
$white: #ffffff;

// 语义色（第一轮）
$color-bg-primary: linear-gradient(to bottom, #1e3c72 0%, #2a5298 55%, #6a11cb 100%);
$color-surface-glass: rgba(255,255,255,0.10);
$color-border-light: rgba(255,255,255,0.20);
$color-text-inverse: $white;
$color-text-muted-inverse: rgba(255,255,255,0.80);

// 间距 (rpx scale)
$space-1: 4rpx; $space-2: 8rpx; $space-3: 12rpx; $space-4: 16rpx; $space-5: 20rpx; $space-6: 24rpx; $space-8: 32rpx; $space-10: 40rpx; $space-12: 48rpx; $space-16: 64rpx;

// 圆角 & 阴影
$radius-md: 20rpx; $radius-lg: 30rpx; $radius-pill: 999rpx; $radius-full: 50%;
$shadow-sm: 0 8rpx 24rpx rgba(0,0,0,0.10);
$shadow-md: 0 12rpx 32rpx rgba(0,0,0,0.16);
$shadow-lg: 0 20rpx 60rpx rgba(0,0,0,0.28);

// 字号（收敛常用）
$font-sm: 28rpx; $font-md: 32rpx; $font-lg: 40rpx; $font-xl: 48rpx; $font-2xl: 60rpx;

// 动效
$ease-standard: cubic-bezier(.4,0,.2,1); $duration-base: 240ms; $duration-fast: 120ms;

// Mixin
@mixin glass($bg:$color-surface-glass,$border:$color-border-light,$blur:10rpx){background:$bg;backdrop-filter:blur($blur);border:1px solid $border;border-radius:$radius-md;}
@mixin gradient-btn($from,$to,$shadow:$shadow-md){background:linear-gradient(135deg,$from 0%,$to 100%);color:$white;font-weight:bold;border:0;border-radius:$radius-pill;box-shadow:$shadow;position:relative;transition:transform $duration-base $ease-standard,box-shadow $duration-base $ease-standard;&:active{transform:translateY(2rpx);box-shadow:$shadow-sm;}}
@mixin hover-lift($dy:-6rpx,$shadow:$shadow-lg){transition:transform $duration-base $ease-standard,box-shadow $duration-base $ease-standard;&:hover{transform:translateY($dy);box-shadow:$shadow;}&:active{transform:translateY(2rpx) scale(.97);}}

// Gradients (semantic wrappers)
$gradient-accent: linear-gradient(135deg,$indigo-500 0%,$violet-600 100%);
$gradient-pink: linear-gradient(135deg,$pink-500 0%,$pink-600 100%);
$gradient-danger: linear-gradient(135deg,$red-500 0%,$red-600 100%);
$gradient-success: linear-gradient(135deg,$green-500 0%,$green-600 100%);

// Elevation mixin for consistent shadows
@mixin elevation($level: md){
	@if $level == sm { box-shadow: $shadow-sm; }
	@else if $level == md { box-shadow: $shadow-md; }
	@else if $level == lg { box-shadow: $shadow-lg; }
}

// Feedback semantic background utilities (glass overlay versions)
.feedback--success { @include glass(); border-color: rgba($green-500,0.35); color: $green-500; }
.feedback--error { @include glass(); border-color: rgba($red-500,0.35); color: $red-500; }
.feedback--info { @include glass(); border-color: rgba($indigo-500,0.35); color: $indigo-500; }
.feedback--warn { @include glass(); border-color: rgba($gold-400,0.4); color: $gold-400; }

// Utility
.app-gradient-bg{background:$color-bg-primary;}
.visually-hidden{position:absolute;width:1px;height:1px;margin:-1px;padding:0;border:0;clip:rect(0 0 0 0);overflow:hidden;}
