# 🚀 快速开始检查清单

按照此清单逐步配置，确保项目能够正常运行。

## ✅ 环境准备

- [ ] **Node.js >= 16.0.0**
  ```bash
  node --version
  ```

- [ ] **包管理器**
  ```bash
  yarn --version  # 推荐
  # 或
  npm --version
  ```

- [ ] **微信开发者工具**（小程序开发必需）
  - 下载地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html

## ✅ 项目安装

- [ ] **克隆项目**
  ```bash
  git clone <your-repo-url>
  cd word-pronunciation-game
  ```

- [ ] **解决 Yarn 工作区问题**
  ```bash
  touch yarn.lock
  ```

- [ ] **安装依赖**
  ```bash
  yarn install
  # 或
  npm install
  ```

## ✅ 必须配置项

### 1. 语音识别 API（🚨 必须配置）

- [ ] **修改接口地址**
  
  文件：`src/services/voice.ts` 第80行
  ```typescript
  url: 'https://your-backend-api.com/asr', // 🚨 替换为你的实际接口
  ```

- [ ] **后端接口要求**
  - [ ] 接受 `multipart/form-data` 格式
  - [ ] 音频字段名：`audio`
  - [ ] 目标单词字段名：`target_word`
  - [ ] 返回格式：`{ success: boolean, text: string, confidence: number }`

### 2. 小程序配置（微信小程序必需）

- [ ] **配置 AppID**
  
  文件：`project.config.json`
  ```json
  {
    "appid": "your-miniprogram-appid" // 🚨 替换为你的小程序 AppID
  }
  ```

- [ ] **配置域名白名单**（微信公众平台）
  - [ ] request合法域名：`https://your-api-server.com`
  - [ ] uploadFile合法域名：`https://your-backend-api.com`
  - [ ] 图片域名：`https://images.unsplash.com`

## ✅ 开发测试

- [ ] **启动开发服务**
  ```bash
  yarn dev:weapp
  # 或
  npm run dev:weapp
  ```

- [ ] **微信开发者工具测试**
  - [ ] 打开 `dist` 目录
  - [ ] 页面正常显示
  - [ ] 单词图片加载正常
  - [ ] 无编译错误

- [ ] **真机测试**（录音功能需要真机）
  - [ ] 生成预览二维码
  - [ ] 微信扫码测试
  - [ ] 录音权限申请成功
  - [ ] 语音识别功能正常

## ✅ 功能验证

- [ ] **基础功能**
  - [ ] 首页正常显示
  - [ ] 点击"开始练习"进入游戏页面
  - [ ] 单词图片显示（不显示中文和音标）
  - [ ] 进度和得分显示正确

- [ ] **录音功能**
  - [ ] 点击"按住说话"按钮
  - [ ] 录音权限申请弹窗
  - [ ] 录音状态反馈
  - [ ] 录音结束后显示"正在识别..."

- [ ] **语音识别**
  - [ ] 识别结果返回
  - [ ] 正确发音显示"太棒了！发音正确！"
  - [ ] 错误发音显示重试提示
  - [ ] 3次错误后显示音标提示

- [ ] **游戏流程**
  - [ ] 完成一个单词后自动进入下一个
  - [ ] 完成所有单词后显示祝贺弹窗
  - [ ] 点击"再玩一次"重新开始
  - [ ] 点击"查看详情"进入结果页面

## ✅ 生产部署

### 微信小程序

- [ ] **生产构建**
  ```bash
  NODE_ENV=production yarn build:weapp
  ```

- [ ] **上传代码**
  - [ ] 微信开发者工具点击"上传"
  - [ ] 填写版本号和备注
  - [ ] 上传成功

- [ ] **提交审核**
  - [ ] 微信公众平台提交审核
  - [ ] 等待审核通过
  - [ ] 发布上线

### H5 部署

- [ ] **构建 H5 版本**
  ```bash
  yarn build:h5
  ```

- [ ] **部署到服务器**
  ```bash
  # 上传 dist 目录到服务器
  scp -r dist/* user@server:/var/www/html/
  ```

- [ ] **配置 Nginx**（参考部署文档）

## 🚨 常见问题排查

### 编译失败

- [ ] **检查 Node.js 版本** >= 16.0.0
- [ ] **清理缓存重新安装**
  ```bash
  rm -rf node_modules yarn.lock
  yarn install
  ```
- [ ] **检查配置文件** `config/index.ts` 中的常量定义

### 录音功能异常

- [ ] **检查权限配置** `src/app.config.ts`
- [ ] **使用真机测试**（开发者工具不支持录音）
- [ ] **检查网络连接**

### 语音识别失败

- [ ] **检查接口地址** `src/services/voice.ts`
- [ ] **验证后端接口**可用性
- [ ] **查看控制台错误**信息
- [ ] **检查域名白名单**配置

### 图片加载失败

- [ ] **检查图片 URL** 是否有效
- [ ] **配置图片域名**白名单
- [ ] **网络连接**是否正常

## 📞 获取帮助

如果遇到问题：

1. **查看文档**
   - `README.md` - 项目说明
   - `docs/DEPLOYMENT_GUIDE.md` - 部署指南
   - `docs/API_DOCUMENTATION.md` - API 文档

2. **检查日志**
   - 微信开发者工具控制台
   - 后端服务器日志
   - 浏览器开发者工具

3. **联系支持**
   - 邮箱：<EMAIL>
   - 微信：your-wechat-id

---

## 🎉 完成！

如果所有检查项都已完成，恭喜你！英语发音练习小程序已经可以正常使用了。

**下一步建议**：
- 添加更多单词到 `src/data/words.json`
- 优化语音识别准确率
- 添加用户数据统计
- 实现更多游戏模式
