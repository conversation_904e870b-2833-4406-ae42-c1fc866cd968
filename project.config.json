{"miniprogramRoot": "dist/", "projectname": "word-pronunciation-game", "description": "English word pronunciation practice mini-program with voice recognition", "appid": "wx3e6eb773e7f9b12e", "setting": {"urlCheck": true, "es6": false, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}