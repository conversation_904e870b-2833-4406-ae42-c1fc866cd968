# 英语发音练习小程序

一个基于 Taro + React 开发的英语发音练习小程序，通过语音识别技术帮助用户提升英语口语发音。

## 上架信息

- 小程序id: wx3e6eb773e7f9b12e

## 📱 功能特性

- 🎤 **语音识别**：实时语音识别，准确评估发音
- 🖼️ **图片猜词**：通过图片猜测英文单词，增加趣味性
- 🏆 **成绩统计**：记录练习成绩，追踪学习进度
- 📚 **丰富词库**：内置25+常用英语单词
- 🎯 **智能反馈**：提供发音建议和纠正
- 📱 **多端支持**：支持微信小程序、H5等多个平台

## 🛠️ 技术栈

- **框架**：Taro 4.1.4 + React 18
- **语言**：TypeScript
- **样式**：Sass
- **构建工具**：Webpack 5
- **包管理**：Yarn / NPM

## 📦 项目结构

```
word-pronunciation-game/
├── src/
│   ├── data/                   # 数据文件
│   │   ├── words.json         # JSON格式单词数据
│   │   └── words.ts           # 兼容性导出
│   ├── pages/                 # 页面组件
│   │   ├── index/             # 首页
│   │   ├── game/              # 游戏页面
│   │   └── result/            # 结果页面
│   ├── services/              # 服务层
│   │   ├── wordService.ts     # 单词数据服务
│   │   ├── voice.ts           # 语音识别服务
│   │   └── api.ts             # API服务
│   ├── types/                 # 类型定义
│   │   └── game.ts
│   └── examples/              # 使用示例
├── config/                    # 配置文件
├── docs/                      # 文档
└── dist/                      # 构建输出
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- Yarn >= 1.22.0 或 NPM >= 8.0.0
- 微信开发者工具（用于小程序开发）

### 安装依赖

```bash
# 克隆项目
git clone <your-repo-url>
cd word-pronunciation-game

# 安装依赖
yarn install
# 或
npm install
```

### 开发运行

```bash
# 微信小程序开发
yarn dev:weapp
# 或
npm run dev:weapp

# H5开发
yarn dev:h5
# 或
npm run dev:h5

# 其他平台
yarn dev:alipay    # 支付宝小程序
yarn dev:swan      # 百度小程序
yarn dev:tt        # 字节跳动小程序
```

### 生产构建

```bash
# 构建微信小程序
yarn build:weapp
# 或
npm run build:weapp

# 构建H5
yarn build:h5
# 或
npm run build:h5
```

## ⚙️ 配置说明

### 1. 基础配置

项目配置文件位于 `config/index.ts`，主要配置项：

```typescript
// config/index.ts
export default defineConfig({
  projectName: 'word-pronunciation-game',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    375: 2,
    828: 1.81 / 2
  },
  // 重要：必须配置这些常量
  defineConstants: {
    ENABLE_INNER_HTML: JSON.stringify(true),
    ENABLE_ADJACENT_HTML: JSON.stringify(true),
    ENABLE_SIZE_APIS: JSON.stringify(true),
    ENABLE_TEMPLATE_CONTENT: JSON.stringify(true),
    ENABLE_CLONE_NODE: JSON.stringify(true),
    ENABLE_CONTAINS: JSON.stringify(true),
    ENABLE_MUTATION_OBSERVER: JSON.stringify(true)
  }
})
```

### 2. 小程序配置

应用配置文件 `src/app.config.ts`：

```typescript
export default {
  pages: [
    'pages/index/index',
    'pages/game/index',
    'pages/result/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#4A90E2',
    navigationBarTitleText: '英语发音练习',
    navigationBarTextStyle: 'white'
  },
  permission: {
    'scope.record': {
      desc: '您的录音权限将用于语音识别功能'
    }
  }
}
```

## 🔌 API 配置

### 语音识别 API

**必须配置**：修改 `src/services/voice.ts` 中的 ASR 接口地址

```typescript
// src/services/voice.ts 第80行
const uploadResult = await Taro.uploadFile({
  url: 'https://your-backend-api.com/asr', // 🚨 替换为你的实际接口
  filePath: audioPath,
  name: 'audio',
  formData: {
    target_word: targetWord
  }
})
```

### API 接口规范

#### 语音识别接口

**请求**：
- **URL**：`POST /asr`
- **Content-Type**：`multipart/form-data`
- **参数**：
  - `audio`：音频文件（mp3格式）
  - `target_word`：目标单词

**响应**：
```json
{
  "success": true,
  "text": "识别出的文本",
  "confidence": 0.95
}
```

#### 单词数据接口（可选）

如果需要从服务器获取单词数据，配置 `src/services/api.ts`：

```typescript
// src/services/api.ts
export const API_CONFIG = {
  BASE_URL: 'https://your-api-server.com/api', // 🚨 替换为你的服务器地址
  ENDPOINTS: {
    WORDS: '/words',
    RANDOM_WORDS: '/words/random',
    SEARCH_WORDS: '/words/search'
  }
}
```

**接口规范**：

1. **获取所有单词**：`GET /words`
2. **获取随机单词**：`GET /words/random?count=5`
3. **搜索单词**：`GET /words/search?q=apple`

**响应格式**：
```json
{
  "words": [
    {
      "id": 1,
      "word": "apple",
      "image": "https://example.com/apple.jpg",
      "pronunciation": "/ˈæpl/",
      "meaning": "苹果"
    }
  ]
}
```

## 📝 开发指南

### 添加新单词

编辑 `src/data/words.json`：

```json
{
  "words": [
    {
      "id": 26,
      "word": "example",
      "image": "https://images.unsplash.com/photo-xxx",
      "pronunciation": "/ɪɡˈzæmpl/",
      "meaning": "例子"
    }
  ]
}
```

### 使用单词服务

```typescript
import { wordService } from './services/wordService'

// 获取随机单词
const randomWords = wordService.getRandomWords(5)

// 搜索单词
const results = wordService.searchWords('apple')

// 按难度获取单词
const easyWords = wordService.getWordsByDifficulty('easy')
```

### 语音识别使用

```typescript
import { voiceService } from './services/voice'

// 检查权限
const hasPermission = await voiceService.checkRecordPermission()

// 开始录音
await voiceService.startRecord()

// 停止录音并识别
const audioPath = await voiceService.stopRecord()
const result = await voiceService.sendToASR(audioPath, 'apple')
```

## 🐛 常见问题

### 1. 编译错误

**问题**：`ENABLE_INNER_HTML is not defined`
**解决**：确保 `config/index.ts` 中配置了所有必需的常量

### 2. 录音权限

**问题**：无法录音
**解决**：
1. 检查 `app.config.ts` 中的权限配置
2. 确保在真机上测试（开发者工具可能不支持录音）

### 3. 语音识别失败

**问题**：识别接口报错
**解决**：
1. 检查 `src/services/voice.ts` 中的接口地址
2. 确保后端接口正常运行
3. 开发阶段会使用模拟数据

### 4. 图片加载失败

**问题**：单词图片无法显示
**解决**：
1. 检查图片URL是否有效
2. 确保网络连接正常
3. 可以替换为本地图片资源

## 📱 部署说明

### 微信小程序

1. 使用微信开发者工具打开 `dist` 目录
2. 配置小程序 AppID
3. 上传代码并提交审核

### H5部署

1. 构建H5版本：`yarn build:h5`
2. 将 `dist` 目录部署到服务器
3. 配置域名和HTTPS

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 微信：your-wechat-id

---

**注意**：使用前请确保配置好语音识别API接口，否则功能将无法正常使用。
