# API 接口文档

本文档详细说明英语发音练习小程序所需的后端 API 接口规范。

## 🎤 语音识别接口（必需）

### 接口概述

用于接收用户录音并返回语音识别结果，这是项目的核心功能接口。

### 请求信息

- **URL**: `/asr`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`

### 请求参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| audio | File | 是 | 音频文件，格式为 MP3 |
| target_word | String | 是 | 目标单词，用于对比识别结果 |

### 请求示例

```javascript
// 前端调用示例
const formData = new FormData()
formData.append('audio', audioFile)
formData.append('target_word', 'apple')

fetch('/asr', {
  method: 'POST',
  body: formData
})
```

### 响应格式

```json
{
  "success": true,
  "text": "apple",
  "confidence": 0.95,
  "message": "识别成功"
}
```

### 响应参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 识别是否成功 |
| text | String | 识别出的文本内容 |
| confidence | Number | 识别置信度 (0-1) |
| message | String | 响应消息（可选） |

### 错误响应

```json
{
  "success": false,
  "text": "",
  "confidence": 0,
  "error": "音频格式不支持",
  "code": "INVALID_AUDIO_FORMAT"
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| INVALID_AUDIO_FORMAT | 音频格式不支持 |
| AUDIO_TOO_LONG | 音频时长超过限制 |
| RECOGNITION_FAILED | 语音识别失败 |
| NETWORK_ERROR | 网络错误 |

## 📚 单词数据接口（可选）

如果需要从服务器动态获取单词数据，可以实现以下接口。

### 1. 获取所有单词

- **URL**: `/words`
- **方法**: `GET`

**响应示例**:
```json
{
  "words": [
    {
      "id": 1,
      "word": "apple",
      "image": "https://example.com/images/apple.jpg",
      "pronunciation": "/ˈæpl/",
      "meaning": "苹果",
      "difficulty": "easy",
      "category": "fruit"
    }
  ],
  "total": 25
}
```

### 2. 获取随机单词

- **URL**: `/words/random`
- **方法**: `GET`
- **参数**: `count` (可选，默认5)

**请求示例**:
```
GET /words/random?count=10
```

**响应格式**: 同获取所有单词

### 3. 搜索单词

- **URL**: `/words/search`
- **方法**: `GET`
- **参数**: `q` (搜索关键词)

**请求示例**:
```
GET /words/search?q=apple
```

### 4. 按难度获取单词

- **URL**: `/words/difficulty/{level}`
- **方法**: `GET`
- **参数**: `level` (easy/medium/hard)

**请求示例**:
```
GET /words/difficulty/easy
```

## 🔧 推荐的语音识别服务

### 1. 百度智能云语音识别

```python
# Python 示例
from aip import AipSpeech

APP_ID = 'your_app_id'
API_KEY = 'your_api_key'
SECRET_KEY = 'your_secret_key'

client = AipSpeech(APP_ID, API_KEY, SECRET_KEY)

def recognize_speech(audio_path):
    with open(audio_path, 'rb') as fp:
        audio_data = fp.read()
    
    result = client.asr(audio_data, 'mp3', 16000, {
        'dev_pid': 1537,  # 英语识别模型
    })
    
    return result
```

### 2. 腾讯云语音识别

```javascript
// Node.js 示例
const tencentcloud = require("tencentcloud-sdk-nodejs")

const AsrClient = tencentcloud.asr.v20190614.Client

const client = new AsrClient({
  credential: {
    secretId: "your_secret_id",
    secretKey: "your_secret_key",
  },
  region: "ap-beijing",
})

async function recognizeSpeech(audioData) {
  const params = {
    EngSerViceType: "16k_en",
    SourceType: 1,
    VoiceFormat: "mp3",
    Data: audioData.toString('base64')
  }
  
  const result = await client.SentenceRecognition(params)
  return result
}
```

### 3. Google Speech-to-Text

```python
# Python 示例
from google.cloud import speech

def recognize_speech(audio_path):
    client = speech.SpeechClient()
    
    with open(audio_path, 'rb') as audio_file:
        content = audio_file.read()
    
    audio = speech.RecognitionAudio(content=content)
    config = speech.RecognitionConfig(
        encoding=speech.RecognitionConfig.AudioEncoding.MP3,
        sample_rate_hertz=16000,
        language_code="en-US",
    )
    
    response = client.recognize(config=config, audio=audio)
    
    return response
```

## 🛠️ 完整后端实现示例

### Node.js + Express 完整示例

```javascript
const express = require('express')
const multer = require('multer')
const cors = require('cors')
const path = require('path')
const fs = require('fs')

const app = express()

// 中间件配置
app.use(cors())
app.use(express.json())

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads'
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir)
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB 限制
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true)
    } else {
      cb(new Error('只支持音频文件'))
    }
  }
})

// 语音识别接口
app.post('/asr', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        text: '',
        confidence: 0,
        error: '未找到音频文件'
      })
    }

    const audioPath = req.file.path
    const targetWord = req.body.target_word

    console.log(`处理音频文件: ${audioPath}, 目标单词: ${targetWord}`)

    // 调用语音识别服务
    const result = await recognizeSpeech(audioPath, targetWord)

    // 清理临时文件
    fs.unlinkSync(audioPath)

    res.json({
      success: true,
      text: result.text,
      confidence: result.confidence,
      message: '识别成功'
    })

  } catch (error) {
    console.error('语音识别错误:', error)
    
    // 清理临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    res.status(500).json({
      success: false,
      text: '',
      confidence: 0,
      error: error.message,
      code: 'RECOGNITION_FAILED'
    })
  }
})

// 模拟语音识别函数（替换为实际的语音识别服务）
async function recognizeSpeech(audioPath, targetWord) {
  // 这里应该调用实际的语音识别服务
  // 例如：百度AI、腾讯云、Google Speech-to-Text等
  
  // 模拟识别结果
  const mockResults = [targetWord, 'wrong_word', 'another_word']
  const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)]
  const confidence = randomResult === targetWord ? 0.9 : 0.3

  return {
    text: randomResult,
    confidence: confidence
  }
}

// 单词数据接口
const words = require('./data/words.json').words

app.get('/words', (req, res) => {
  res.json({
    words: words,
    total: words.length
  })
})

app.get('/words/random', (req, res) => {
  const count = parseInt(req.query.count) || 5
  const shuffled = [...words].sort(() => Math.random() - 0.5)
  const randomWords = shuffled.slice(0, Math.min(count, words.length))
  
  res.json({
    words: randomWords,
    total: randomWords.length
  })
})

app.get('/words/search', (req, res) => {
  const query = req.query.q?.toLowerCase() || ''
  const results = words.filter(word => 
    word.word.toLowerCase().includes(query) ||
    word.meaning.includes(query)
  )
  
  res.json({
    words: results,
    total: results.length
  })
})

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    success: false,
    error: '服务器内部错误',
    code: 'INTERNAL_SERVER_ERROR'
  })
})

// 启动服务器
const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`API 服务器运行在端口 ${PORT}`)
  console.log(`语音识别接口: http://localhost:${PORT}/asr`)
  console.log(`单词数据接口: http://localhost:${PORT}/words`)
})

module.exports = app
```

## 🔒 安全建议

### 1. 文件上传安全

- 限制文件大小和类型
- 验证文件内容
- 使用临时目录存储
- 及时清理临时文件

### 2. API 安全

- 实现请求频率限制
- 添加身份验证
- 使用 HTTPS
- 验证请求参数

### 3. 错误处理

- 不暴露敏感信息
- 统一错误响应格式
- 记录详细日志

## 📊 性能优化

### 1. 缓存策略

- 缓存常用单词数据
- 使用 CDN 加速图片加载
- 实现 API 响应缓存

### 2. 并发处理

- 使用异步处理
- 实现请求队列
- 优化数据库查询

---

按照此文档实现后端 API，你的英语发音练习小程序就能正常工作了！
