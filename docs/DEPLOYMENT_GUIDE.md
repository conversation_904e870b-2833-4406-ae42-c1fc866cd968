# 部署配置指南

本文档详细说明如何部署和配置英语发音练习小程序。

## 🔧 环境配置

### 1. 开发环境设置

```bash
# 检查 Node.js 版本
node --version  # 需要 >= 16.0.0

# 检查包管理器
yarn --version  # 推荐使用 Yarn
npm --version   # 或使用 NPM

# 安装 Taro CLI（如果没有安装）
npm install -g @tarojs/cli
```

### 2. 项目初始化

```bash
# 克隆项目
git clone <your-repo-url>
cd word-pronunciation-game

# 创建空的 yarn.lock（解决工作区问题）
touch yarn.lock

# 安装依赖
yarn install
```

## 🚨 必须配置的项目

### 1. 语音识别 API（必须）

**文件**：`src/services/voice.ts`

```typescript
// 第80行，替换为你的实际接口
const uploadResult = await Taro.uploadFile({
  url: 'https://your-backend-api.com/asr', // 🚨 必须修改
  filePath: audioPath,
  name: 'audio',
  formData: {
    target_word: targetWord
  }
})
```

**后端接口要求**：
- 接受 multipart/form-data 格式
- 音频文件字段名：`audio`
- 目标单词字段名：`target_word`
- 返回 JSON 格式：`{ success: boolean, text: string, confidence: number }`

### 2. 小程序 AppID（微信小程序必须）

**文件**：`project.config.json`

```json
{
  "appid": "your-miniprogram-appid", // 🚨 替换为你的小程序 AppID
  "projectname": "word-pronunciation-game",
  "setting": {
    "urlCheck": false,
    "es6": false,
    "enhance": false,
    "compileHotReLoad": false
  }
}
```

## 📱 平台部署

### 微信小程序部署

#### 1. 开发阶段

```bash
# 启动开发服务
yarn dev:weapp

# 使用微信开发者工具打开 dist 目录
# 工具 -> 构建 npm -> 确认构建成功
```

#### 2. 生产部署

```bash
# 生产构建
NODE_ENV=production yarn build:weapp

# 在微信开发者工具中：
# 1. 打开 dist 目录
# 2. 点击"上传"按钮
# 3. 填写版本号和项目备注
# 4. 上传成功后在微信公众平台提交审核
```

#### 3. 域名配置

在微信公众平台配置服务器域名：

```
开发 -> 开发管理 -> 开发设置 -> 服务器域名

request合法域名：
- https://your-api-server.com
- https://images.unsplash.com  (如果使用 Unsplash 图片)

uploadFile合法域名：
- https://your-backend-api.com  (语音识别接口域名)
```

### H5 部署

#### 1. 构建

```bash
# H5 构建
yarn build:h5
```

#### 2. 服务器部署

```bash
# 将 dist 目录上传到服务器
scp -r dist/* user@your-server:/var/www/html/

# 或使用 rsync
rsync -av dist/ user@your-server:/var/www/html/
```

#### 3. Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔌 后端 API 实现示例

### Node.js + Express 示例

```javascript
const express = require('express')
const multer = require('multer')
const app = express()

// 配置文件上传
const upload = multer({ dest: 'uploads/' })

// 语音识别接口
app.post('/asr', upload.single('audio'), async (req, res) => {
  try {
    const audioFile = req.file
    const targetWord = req.body.target_word
    
    // 这里调用你的语音识别服务
    // 例如：百度语音识别、腾讯云ASR、阿里云ASR等
    const result = await recognizeSpeech(audioFile.path, targetWord)
    
    res.json({
      success: true,
      text: result.text,
      confidence: result.confidence
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      text: '',
      confidence: 0,
      error: error.message
    })
  }
})

// 模拟语音识别函数
async function recognizeSpeech(audioPath, targetWord) {
  // 这里集成实际的语音识别服务
  // 返回识别结果
  return {
    text: 'apple', // 识别出的文本
    confidence: 0.95 // 置信度
  }
}

app.listen(3000, () => {
  console.log('ASR API server running on port 3000')
})
```

### Python + Flask 示例

```python
from flask import Flask, request, jsonify
import os

app = Flask(__name__)

@app.route('/asr', methods=['POST'])
def speech_recognition():
    try:
        audio_file = request.files['audio']
        target_word = request.form['target_word']
        
        # 保存音频文件
        audio_path = f"uploads/{audio_file.filename}"
        audio_file.save(audio_path)
        
        # 调用语音识别服务
        result = recognize_speech(audio_path, target_word)
        
        # 清理临时文件
        os.remove(audio_path)
        
        return jsonify({
            'success': True,
            'text': result['text'],
            'confidence': result['confidence']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'text': '',
            'confidence': 0,
            'error': str(e)
        }), 500

def recognize_speech(audio_path, target_word):
    # 这里集成实际的语音识别服务
    # 例如：百度AI、腾讯云、Google Speech-to-Text等
    return {
        'text': 'apple',
        'confidence': 0.95
    }

if __name__ == '__main__':
    app.run(debug=True, port=3000)
```

## 🔍 测试指南

### 1. 本地测试

```bash
# 启动开发服务
yarn dev:weapp

# 在微信开发者工具中测试：
# 1. 检查页面是否正常显示
# 2. 测试录音权限申请
# 3. 测试语音识别功能（需要真机）
```

### 2. 真机测试

```bash
# 生成预览二维码
# 在微信开发者工具中点击"预览"
# 使用微信扫码在真机上测试
```

### 3. 功能测试清单

- [ ] 页面正常加载
- [ ] 单词图片显示正常
- [ ] 录音权限申请成功
- [ ] 录音功能正常
- [ ] 语音识别返回结果
- [ ] 游戏流程完整
- [ ] 成绩统计正确

## 🚨 常见部署问题

### 1. 编译失败

**问题**：`yarn dev:weapp` 编译失败
**解决方案**：
```bash
# 清理缓存
rm -rf node_modules/.cache dist
yarn install
yarn dev:weapp
```

### 2. 权限问题

**问题**：录音权限被拒绝
**解决方案**：
- 确保在 `app.config.ts` 中配置了录音权限
- 在真机上测试（开发者工具不支持录音）

### 3. 网络请求失败

**问题**：API 请求被拒绝
**解决方案**：
- 检查小程序域名白名单配置
- 确保使用 HTTPS 协议
- 检查服务器 CORS 配置

### 4. 图片加载失败

**问题**：单词图片无法显示
**解决方案**：
- 将图片域名添加到小程序白名单
- 使用 CDN 或本地图片资源

## 📊 性能优化

### 1. 图片优化

```typescript
// 使用适当尺寸的图片
const imageUrl = `https://images.unsplash.com/photo-xxx?w=400&h=400&fit=crop`

// 或使用本地图片
import appleImg from '../assets/images/apple.jpg'
```

### 2. 代码分割

```typescript
// 使用动态导入
const LazyComponent = lazy(() => import('./LazyComponent'))
```

### 3. 缓存配置

```typescript
// config/index.ts
export default defineConfig({
  cache: {
    enable: true // 开启 Webpack 持久化缓存
  }
})
```

## 📈 监控和分析

### 1. 小程序数据分析

在微信公众平台查看：
- 用户访问数据
- 页面访问路径
- 用户留存率

### 2. 错误监控

```typescript
// 添加全局错误处理
Taro.onError((error) => {
  console.error('小程序错误:', error)
  // 上报错误到监控平台
})
```

---

按照本指南配置后，你的英语发音练习小程序就可以正常运行了！
